# 🚀 Supabase Setup Guide voor Quote.AI+CRM

## 📋 Stap 1: Supabase Project Aanmaken

1. Ga naar [supabase.com](https://supabase.com)
2. <PERSON><PERSON> op "Start your project"
3. <PERSON>ak een account aan of log in
4. <PERSON><PERSON> op "New Project"
5. Vul in:
   - **Project name**: `quote-ai-crm`
   - **Database password**: <PERSON><PERSON> een sterk wachtwoord
   - **Region**: <PERSON><PERSON> de dichtstbijzijnde regio
6. <PERSON><PERSON> op "Create new project"

## 🔑 Stap 2: API Keys Ophalen

1. Ga naar je project dashboard
2. <PERSON><PERSON> op "Settings" in de sidebar
3. <PERSON><PERSON> op "API"
4. <PERSON><PERSON>er de volgende waarden:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **anon public key**: `eyJ...`
   - **service_role key**: `eyJ...` (alleen voor server-side gebruik)

## 🔧 Stap 3: Environment Variables Instellen

Update je `.env.local` bestand:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# NextAuth Configuration
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Database URL (Supabase PostgreSQL)
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project-id.supabase.co:5432/postgres
```

## 🗄️ Stap 4: Database Schema Migreren

1. Installeer Prisma CLI (als nog niet gedaan):
```bash
npm install -g prisma
```

2. Genereer Prisma client:
```bash
npx prisma generate
```

3. Push schema naar Supabase:
```bash
npx prisma db push
```

4. Seed de database:
```bash
npx prisma db seed
```

## 🔐 Stap 5: Authentication Instellen

### Email/Password Auth (Standaard ingeschakeld)

1. Ga naar "Authentication" > "Settings" in Supabase dashboard
2. Zorg dat "Enable email confirmations" is ingeschakeld
3. Configureer email templates naar wens

### Google OAuth (Optioneel)

1. Ga naar [Google Cloud Console](https://console.cloud.google.com)
2. Maak een nieuw project of selecteer bestaand project
3. Ga naar "APIs & Services" > "Credentials"
4. Klik "Create Credentials" > "OAuth 2.0 Client IDs"
5. Configureer:
   - **Application type**: Web application
   - **Authorized redirect URIs**: 
     - `https://your-project-id.supabase.co/auth/v1/callback`
     - `http://localhost:3000/auth/callback`
6. Kopieer Client ID en Client Secret
7. In Supabase dashboard:
   - Ga naar "Authentication" > "Providers"
   - Schakel Google in
   - Vul Client ID en Client Secret in

## 🚀 Stap 6: Applicatie Starten

1. Start development server:
```bash
npm run dev
```

2. Ga naar `http://localhost:3000`
3. Test de login functionaliteit

## ✅ Verificatie Checklist

- [ ] Supabase project aangemaakt
- [ ] Environment variables ingesteld
- [ ] Database schema gemigreerd
- [ ] Seed data toegevoegd
- [ ] Email auth werkt
- [ ] Google OAuth geconfigureerd (optioneel)
- [ ] Login/logout functionaliteit getest

## 🔧 Troubleshooting

### Database Connection Issues
- Controleer of DATABASE_URL correct is
- Zorg dat je database password geen speciale karakters bevat
- Test connectie met: `npx prisma db pull`

### Auth Issues
- Controleer of NEXTAUTH_SECRET is ingesteld
- Zorg dat redirect URLs correct zijn geconfigureerd
- Check browser console voor errors

### Supabase API Issues
- Controleer of API keys correct zijn
- Zorg dat project URL correct is
- Test API connectie in browser network tab

## 📚 Nuttige Links

- [Supabase Documentation](https://supabase.com/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [NextAuth.js Documentation](https://next-auth.js.org)
