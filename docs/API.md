# Quote.AI+CRM API Documentation

## Overview

The Quote.AI+CRM API provides a comprehensive backend for managing customers, properties, quotes, and AI-powered features. All endpoints require authentication unless otherwise specified.

## Authentication

The API uses NextAuth.js for authentication. Include the session token in your requests.

### Headers
```
Authorization: Bearer <session-token>
Content-Type: application/json
```

## Rate Limiting

- Global rate limit: 1000 requests per 15 minutes per IP
- API-specific limits vary by endpoint
- Rate limit headers are included in responses:
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Unix timestamp when the rate limit resets

## Error Handling

All errors follow a consistent format:

```json
{
  "error": "Error message",
  "details": "Additional error details (optional)"
}
```

### HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `429`: Too Many Requests
- `500`: Internal Server Error

## Endpoints

### Authentication

#### POST /api/auth/register
Register a new user and organization.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword",
  "organizationName": "My Company",
  "organizationSlug": "my-company",
  "phone": "+1234567890",
  "address": "123 Main St"
}
```

**Response:**
```json
{
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "organization": {
    "id": "org-id",
    "name": "My Company",
    "slug": "my-company"
  }
}
```

### Customers

#### GET /api/customers
Get paginated list of customers.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `search`: Search term for name, email, or company
- `sortBy`: Sort field (name, email, company, createdAt)
- `sortOrder`: Sort direction (asc, desc)

**Response:**
```json
{
  "data": [
    {
      "id": "customer-id",
      "name": "Customer Name",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "company": "Customer Company",
      "address": "Customer Address",
      "notes": "Customer notes",
      "createdAt": "2024-01-01T00:00:00Z",
      "createdBy": {
        "name": "User Name"
      },
      "_count": {
        "quotes": 5,
        "properties": 2
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

#### POST /api/customers
Create a new customer.

**Request Body:**
```json
{
  "name": "Customer Name",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "company": "Customer Company",
  "address": {
    "street": "123 Main St",
    "city": "City",
    "postalCode": "12345",
    "country": "Country"
  },
  "notes": "Customer notes"
}
```

#### GET /api/customers/[id]
Get a specific customer by ID.

#### PUT /api/customers/[id]
Update a customer.

#### DELETE /api/customers/[id]
Delete a customer (only if no associated quotes or properties).

### Properties

#### GET /api/properties
Get paginated list of properties.

**Query Parameters:**
- Same as customers endpoint
- `customerId`: Filter by customer ID

#### POST /api/properties
Create a new property.

**Request Body:**
```json
{
  "name": "Property Name",
  "address": "Property Address",
  "description": "Property description",
  "propertyType": "residential",
  "sizeM2": 150.5,
  "customerId": "customer-id"
}
```

### Quotes

#### GET /api/quotes
Get paginated list of quotes.

**Query Parameters:**
- Same as customers endpoint
- `status`: Filter by status (DRAFT, SENT, ACCEPTED, REJECTED, EXPIRED)
- `customerId`: Filter by customer ID

#### POST /api/quotes
Create a new quote.

**Request Body:**
```json
{
  "title": "Quote Title",
  "description": "Quote description",
  "customerId": "customer-id",
  "propertyId": "property-id",
  "items": [
    {
      "beschrijving": "Item description",
      "aantal": 2,
      "eenheid": "stuk",
      "eenheidPrijs": 100.00,
      "volgorde": 1
    }
  ],
  "btwPercentage": 21,
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

#### GET /api/quotes/[id]
Get a specific quote with all details.

#### PUT /api/quotes/[id]
Update a quote.

### AI Integration

#### POST /api/ai/chat
Send a message to the AI assistant.

**Request Body:**
```json
{
  "message": "Help me create a quote for bathroom renovation",
  "conversationId": "conversation-id",
  "context": {
    "customerId": "customer-id",
    "propertyId": "property-id"
  }
}
```

**Response:**
```json
{
  "response": {
    "content": "AI response content",
    "type": "text",
    "metadata": {}
  },
  "conversationId": "conversation-id"
}
```

### Messaging

#### POST /api/messaging/email
Send an email.

**Request Body:**
```json
{
  "to": "<EMAIL>",
  "subject": "Email Subject",
  "content": "Email content",
  "html": "<p>HTML content</p>",
  "customerId": "customer-id",
  "quoteId": "quote-id",
  "templateId": "template-id"
}
```

#### POST /api/messaging/whatsapp
Send a WhatsApp message.

**Request Body:**
```json
{
  "to": "+1234567890",
  "message": "Message content",
  "type": "text",
  "customerId": "customer-id",
  "quoteId": "quote-id"
}
```

#### GET /api/messaging/status
Get messaging system status and statistics.

### Analytics

#### GET /api/dashboard/stats
Get dashboard statistics.

**Response:**
```json
{
  "stats": {
    "customers": {
      "total": 100,
      "new": 5,
      "growth": 10
    },
    "quotes": {
      "total": 250,
      "value": 125000.00,
      "pending": 15,
      "accepted": 200,
      "rejected": 20,
      "growth": 15
    },
    "revenue": {
      "total": 100000.00,
      "thisMonth": 15000.00,
      "lastMonth": 12000.00,
      "growth": 25
    }
  }
}
```

#### GET /api/analytics/charts
Get chart data for analytics.

**Query Parameters:**
- `type`: Chart type (quote-status, revenue-over-time, activity-trends)
- `period`: Time period (daily, weekly, monthly)
- `range`: Predefined range (thisWeek, thisMonth, thisYear, etc.)
- `startDate`: Custom start date (ISO string)
- `endDate`: Custom end date (ISO string)

#### GET /api/analytics/reports
Generate detailed reports.

**Query Parameters:**
- `type`: Report type (top-customers, quote-summary, revenue-summary, activity-summary)
- `startDate`: Start date (required)
- `endDate`: End date (required)
- `limit`: Number of items to include

#### GET /api/analytics/export
Export data in CSV or JSON format.

**Query Parameters:**
- `type`: Data type (customers, quotes, activities, revenue)
- `format`: Export format (csv, json)
- `startDate`: Start date
- `endDate`: End date
- `range`: Predefined range

### Security

#### GET /api/security/audit-logs
Get audit logs (Admin/Owner only).

**Query Parameters:**
- `action`: Filter by action type
- `resource`: Filter by resource type
- `userId`: Filter by user ID
- `startDate`: Start date
- `endDate`: End date
- `limit`: Number of logs to return

#### GET /api/security/report
Generate security report (Owner only).

**Query Parameters:**
- `startDate`: Start date (required)
- `endDate`: End date (required)
- `format`: Report format (json, csv)

## Webhooks

### WhatsApp Webhook

#### GET /api/messaging/whatsapp/webhook
Webhook verification endpoint for WhatsApp.

#### POST /api/messaging/whatsapp/webhook
Receive incoming WhatsApp messages.

## Data Models

### Customer
```typescript
{
  id: string
  name: string
  email?: string
  phone?: string
  company?: string
  address?: string
  notes?: string
  organizationId: string
  createdById?: string
  createdAt: Date
  updatedAt: Date
}
```

### Quote
```typescript
{
  id: string
  number: string
  title: string
  description?: string
  status: 'DRAFT' | 'SENT' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED'
  basisPrijs: number
  btwPercentage: number
  btwBedrag: number
  totaalPrijs: number
  aiGenerated: boolean
  organizationId: string
  userId: string
  customerId: string
  propertyId?: string
  createdAt: Date
  updatedAt: Date
  sentAt?: Date
  expiresAt?: Date
}
```

### QuoteItem
```typescript
{
  id: string
  beschrijving: string
  aantal: number
  eenheid: string
  eenheidPrijs: number
  totaalPrijs: number
  volgorde: number
  quoteId: string
}
```

## SDKs and Libraries

### JavaScript/TypeScript
```bash
npm install @quote-ai-crm/sdk
```

### Usage Example
```typescript
import { QuoteAICRM } from '@quote-ai-crm/sdk'

const client = new QuoteAICRM({
  apiUrl: 'https://api.quote-ai-crm.com',
  apiKey: 'your-api-key'
})

// Get customers
const customers = await client.customers.list({
  page: 1,
  limit: 10
})

// Create a quote
const quote = await client.quotes.create({
  title: 'Bathroom Renovation',
  customerId: 'customer-id',
  items: [
    {
      beschrijving: 'Tiles',
      aantal: 20,
      eenheidPrijs: 25.00
    }
  ]
})
```
