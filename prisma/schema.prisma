// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Organizations (multi-tenant root)
model Organization {
  id          String   @id @default(cuid())
  name        String   
  slug        String   @unique
  domain      String?  @unique
  plan        String   @default("FREE") // FREE, STARTER, PROFESSIONAL, ENTERPRISE
  status      String   @default("TRIAL") // TRIAL, ACTIVE, SUSPENDED
  maxUsers    Int      @default(3)
  maxQuotes   Int      @default(50)
  maxAiUsage  Int      @default(100)
  ownerId     String?
  
  // Contact Information
  phone       String?
  address     String?
  
  // Email Settings
  smtpHost        String?
  smtpPort        Int?
  smtpUsername    String?
  smtpPassword    String?
  emailFromAddress String?
  
  // WhatsApp Settings
  whatsappToken         String?
  whatsappPhoneId       String?
  whatsappBusinessId    String?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  profiles    Profile[]
  customers   Customer[]
  properties  Property[]
  quotes      Quote[]
  activities  Activity[]
  emailTemplates EmailTemplate[]
  aiConversations AiConversation[]
  messageLogs MessageLog[]
  integrations Integration[]
  
  @@map("organizations")
}

// Profiles (extends auth.users)
model Profile {
  id            String   @id @default(cuid())
  email         String   @unique
  password      String?  // Hashed password
  name          String?
  avatarUrl     String?
  organizationId String?
  role          String   @default("USER") // OWNER, ADMIN, USER, VIEWER
  phone         String?
  preferences   String?  // JSON stored as string
  isActive      Boolean  @default(true)
  lastLoginAt   DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relations
  organization  Organization? @relation(fields: [organizationId], references: [id])
  customers     Customer[]
  quotes        Quote[]
  activities    Activity[]
  aiConversations AiConversation[]
  messageLogs   MessageLog[]
  
  @@map("profiles")
}

// Customers
model Customer {
  id            String   @id @default(cuid())
  name          String   
  email         String?
  phone         String?
  company       String?
  address       String?  // JSON stored as string
  notes         String?
  organizationId String   
  createdById   String?
  createdAt     DateTime @default(now())
  
  // Relations
  organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdBy     Profile?     @relation(fields: [createdById], references: [id])
  properties    Property[]
  quotes        Quote[]
  activities    Activity[]
  messageLogs   MessageLog[]
  
  @@map("customers")
}

// Properties
model Property {
  id            String   @id @default(cuid())
  name          String   
  address       String   
  description   String?
  propertyType  String?
  sizeM2        Int?
  customerId    String   
  organizationId String   
  createdAt     DateTime @default(now())
  
  // Relations
  customer      Customer     @relation(fields: [customerId], references: [id], onDelete: Cascade)
  organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  quotes        Quote[]
  
  @@map("properties")
}

// Quotes
model Quote {
  id            String   @id @default(cuid())
  number        String   
  title         String   
  description   String?
  status        String   @default("DRAFT") // DRAFT, SENT, ACCEPTED, REJECTED, EXPIRED
  
  // Pricing
  basisPrijs    Float    
  btwPercentage Int      @default(21)
  btwBedrag     Float    
  totaalPrijs   Float    
  
  // AI Data
  aiGenerated   Boolean  @default(false)
  aiPrompt      String?
  aiTokensUsed  Int?
  aiModelUsed   String?
  
  // Relations
  organizationId String   
  userId        String   
  customerId    String   
  propertyId    String?
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  sentAt        DateTime?
  expiresAt     DateTime?
  
  // Relations
  organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user          Profile      @relation(fields: [userId], references: [id])
  customer      Customer     @relation(fields: [customerId], references: [id], onDelete: Cascade)
  property      Property?    @relation(fields: [propertyId], references: [id])
  items         QuoteItem[]
  activities    Activity[]
  messageLogs   MessageLog[]
  
  @@unique([organizationId, number])
  @@map("quotes")
}

// Quote Items
model QuoteItem {
  id            String   @id @default(cuid())
  quoteId       String   
  beschrijving  String   
  aantal        Int      @default(1)
  eenheid       String   @default("stuk")
  eenheidPrijs  Float    
  totaalPrijs   Float    
  volgorde      Int      @default(0)
  createdAt     DateTime @default(now())
  
  // Relations
  quote         Quote     @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  
  @@map("quote_items")
}

// Activities (CRM)
model Activity {
  id            String   @id @default(cuid())
  type          String   // NOTE, CALL, EMAIL, MEETING, TASK, WHATSAPP
  title         String   
  description   String?
  completed     Boolean  @default(false)
  dueDate       DateTime?
  metadata      String?  // JSON stored as string
  
  // Relations
  organizationId String   
  userId        String   
  customerId    String?
  quoteId       String?
  
  createdAt     DateTime @default(now())
  
  // Relations
  organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user          Profile      @relation(fields: [userId], references: [id])
  customer      Customer?    @relation(fields: [customerId], references: [id])
  quote         Quote?       @relation(fields: [quoteId], references: [id])
  
  @@map("activities")
}

// Email Templates
model EmailTemplate {
  id            String   @id @default(cuid())
  name          String   
  subject       String   
  content       String   
  templateType  String?  // QUOTE, INVOICE, REMINDER, WELCOME
  organizationId String   
  isDefault     Boolean  @default(false)
  createdAt     DateTime @default(now())
  
  // Relations
  organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@map("email_templates")
}

// AI Conversations
model AiConversation {
  id            String   @id @default(cuid())
  title         String?
  messages      String   @default("[]") // JSON stored as string
  context       String?  // JSON stored as string
  organizationId String
  userId        String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user          Profile      @relation(fields: [userId], references: [id])

  @@map("ai_conversations")
}

// Message Log (for WhatsApp, Email, SMS)
model MessageLog {
  id            String   @id @default(cuid())
  type          String   // EMAIL, WHATSAPP, SMS
  direction     String   // INBOUND, OUTBOUND
  status        String   // PENDING, SENT, DELIVERED, FAILED, READ
  subject       String?
  content       String
  metadata      String?  // JSON stored as string (headers, attachments, etc.)

  // Recipients/Sender
  fromAddress   String?
  toAddress     String?
  phoneNumber   String?

  // Relations
  organizationId String
  customerId    String?
  quoteId       String?
  userId        String?

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  customer      Customer?    @relation(fields: [customerId], references: [id])
  quote         Quote?       @relation(fields: [quoteId], references: [id])
  user          Profile?     @relation(fields: [userId], references: [id])

  @@map("message_logs")
}

// API Keys & Integrations
model Integration {
  id            String   @id @default(cuid())
  name          String   // OPENAI, WHATSAPP, SMTP, etc.
  type          String   // AI, MESSAGING, PAYMENT, etc.
  config        String   // JSON stored as string
  isActive      Boolean  @default(true)
  organizationId String

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, name])
  @@map("integrations")
}