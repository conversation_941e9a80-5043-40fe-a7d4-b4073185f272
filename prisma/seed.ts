import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()

async function main() {
  // Maak een demo-organisatie aan
  const org = await prisma.organization.upsert({
    where: { slug: 'demo-org' },
    update: {},
    create: {
      name: 'De<PERSON> Bedrijf',
      slug: 'demo-org',
      plan: 'FREE',
      status: 'ACTIVE',
    },
  })

  // Maak een demo-gebruiker aan
  await prisma.profile.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo User',
      organizationId: org.id,
      role: 'OWNER',
    },
  })
}

main()
  .catch(e => {
    console.error(e)
    process.exit(1)
  })
  .finally(() => prisma.$disconnect())
