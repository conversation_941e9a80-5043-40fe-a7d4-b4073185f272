export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      Organization: {
        Row: {
          id: string
          name: string
          slug: string
          domain: string | null
          plan: string
          status: string
          maxUsers: number
          maxQuotes: number
          maxAiUsage: number
          ownerId: string | null
          phone: string | null
          address: string | null
          smtpHost: string | null
          smtpPort: number | null
          smtpUsername: string | null
          smtpPassword: string | null
          emailFromAddress: string | null
          whatsappToken: string | null
          whatsappPhoneId: string | null
          whatsappBusinessId: string | null
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          domain?: string | null
          plan?: string
          status?: string
          maxUsers?: number
          maxQuotes?: number
          maxAiUsage?: number
          ownerId?: string | null
          phone?: string | null
          address?: string | null
          smtpHost?: string | null
          smtpPort?: number | null
          smtpUsername?: string | null
          smtpPassword?: string | null
          emailFromAddress?: string | null
          whatsappToken?: string | null
          whatsappPhoneId?: string | null
          whatsappBusinessId?: string | null
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          domain?: string | null
          plan?: string
          status?: string
          maxUsers?: number
          maxQuotes?: number
          maxAiUsage?: number
          ownerId?: string | null
          phone?: string | null
          address?: string | null
          smtpHost?: string | null
          smtpPort?: number | null
          smtpUsername?: string | null
          smtpPassword?: string | null
          emailFromAddress?: string | null
          whatsappToken?: string | null
          whatsappPhoneId?: string | null
          whatsappBusinessId?: string | null
          createdAt?: string
          updatedAt?: string
        }
      }
      Profile: {
        Row: {
          id: string
          email: string
          name: string | null
          phone: string | null
          role: string
          organizationId: string
          lastLoginAt: string | null
          isActive: boolean
          preferences: Json | null
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          phone?: string | null
          role?: string
          organizationId: string
          lastLoginAt?: string | null
          isActive?: boolean
          preferences?: Json | null
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          phone?: string | null
          role?: string
          organizationId?: string
          lastLoginAt?: string | null
          isActive?: boolean
          preferences?: Json | null
          createdAt?: string
          updatedAt?: string
        }
      }
      Customer: {
        Row: {
          id: string
          name: string
          email: string | null
          phone: string | null
          address: string | null
          city: string | null
          postalCode: string | null
          country: string | null
          organizationId: string
          notes: string | null
          tags: string[] | null
          isActive: boolean
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          postalCode?: string | null
          country?: string | null
          organizationId: string
          notes?: string | null
          tags?: string[] | null
          isActive?: boolean
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          postalCode?: string | null
          country?: string | null
          organizationId?: string
          notes?: string | null
          tags?: string[] | null
          isActive?: boolean
          createdAt?: string
          updatedAt?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
