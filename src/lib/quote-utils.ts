import { db } from '@/lib/db'

export interface QuoteItem {
  beschrijving: string
  aantal: number
  eenheid: string
  eenheidPrijs: number
  volgorde?: number
}

export interface QuoteCalculation {
  basisPrijs: number
  btwPercentage: number
  btwBedrag: number
  totaalPrijs: number
}

// Generate unique quote number
export async function generateQuoteNumber(organizationId: string): Promise<string> {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  
  // Get the count of quotes for this organization this month
  const startOfMonth = new Date(year, now.getMonth(), 1)
  const endOfMonth = new Date(year, now.getMonth() + 1, 0)
  
  const monthlyCount = await db.quote.count({
    where: {
      organizationId,
      createdAt: {
        gte: startOfMonth,
        lte: endOfMonth
      }
    }
  })
  
  const sequenceNumber = String(monthlyCount + 1).padStart(3, '0')
  return `QUOTE-${year}${month}-${sequenceNumber}`
}

// Calculate quote totals from items
export function calculateQuoteTotals(items: QuoteItem[], btwPercentage: number = 21): QuoteCalculation {
  const basisPrijs = items.reduce((total, item) => {
    const itemTotal = item.aantal * item.eenheidPrijs
    return total + itemTotal
  }, 0)
  
  const btwBedrag = (basisPrijs * btwPercentage) / 100
  const totaalPrijs = basisPrijs + btwBedrag
  
  return {
    basisPrijs: Math.round(basisPrijs * 100) / 100, // Round to 2 decimal places
    btwPercentage,
    btwBedrag: Math.round(btwBedrag * 100) / 100,
    totaalPrijs: Math.round(totaalPrijs * 100) / 100
  }
}

// Validate quote items and calculate totals
export function processQuoteItems(items: any[]): { items: QuoteItem[], totals: QuoteCalculation } {
  const processedItems: QuoteItem[] = items.map((item, index) => ({
    beschrijving: item.beschrijving || '',
    aantal: Math.max(1, Number(item.aantal) || 1),
    eenheid: item.eenheid || 'stuk',
    eenheidPrijs: Math.max(0, Number(item.eenheidPrijs) || 0),
    volgorde: item.volgorde || index + 1
  }))
  
  const btwPercentage = 21 // Default BTW percentage
  const totals = calculateQuoteTotals(processedItems, btwPercentage)
  
  return { items: processedItems, totals }
}

// Generate quote expiry date (default 30 days from now)
export function generateExpiryDate(days: number = 30): Date {
  const expiryDate = new Date()
  expiryDate.setDate(expiryDate.getDate() + days)
  return expiryDate
}

// Check if quote is expired
export function isQuoteExpired(expiresAt: Date | null): boolean {
  if (!expiresAt) return false
  return new Date() > expiresAt
}

// Get quote status based on dates and current status
export function getQuoteStatus(status: string, expiresAt: Date | null, sentAt: Date | null): string {
  if (status === 'ACCEPTED' || status === 'REJECTED') {
    return status
  }
  
  if (isQuoteExpired(expiresAt)) {
    return 'EXPIRED'
  }
  
  if (sentAt && status === 'DRAFT') {
    return 'SENT'
  }
  
  return status
}

// Format quote number for display
export function formatQuoteNumber(number: string): string {
  return number.replace('QUOTE-', '#')
}

// Generate quote PDF filename
export function generateQuoteFilename(quoteNumber: string, customerName: string): string {
  const sanitizedCustomer = customerName.replace(/[^a-zA-Z0-9]/g, '_')
  const formattedNumber = formatQuoteNumber(quoteNumber)
  return `quote_${formattedNumber}_${sanitizedCustomer}.pdf`
}

// Validate quote status transition
export function isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
  const validTransitions: Record<string, string[]> = {
    'DRAFT': ['SENT', 'REJECTED'],
    'SENT': ['ACCEPTED', 'REJECTED', 'EXPIRED'],
    'ACCEPTED': [], // Final state
    'REJECTED': ['DRAFT'], // Can be reopened
    'EXPIRED': ['DRAFT'] // Can be reopened
  }
  
  return validTransitions[currentStatus]?.includes(newStatus) || false
}
