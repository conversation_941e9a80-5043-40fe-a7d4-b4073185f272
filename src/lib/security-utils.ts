import crypto from 'crypto'
import { NextRequest } from 'next/server'

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000) // Limit length
}

// SQL injection prevention (basic)
export function sanitizeSqlInput(input: string): string {
  if (typeof input !== 'string') return ''
  
  return input
    .replace(/['";\\]/g, '') // Remove SQL special characters
    .replace(/--/g, '') // Remove SQL comments
    .replace(/\/\*/g, '') // Remove SQL block comments start
    .replace(/\*\//g, '') // Remove SQL block comments end
    .trim()
}

// XSS prevention
export function escapeHtml(unsafe: string): string {
  if (typeof unsafe !== 'string') return ''
  
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
}

// Generate secure random token
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex')
}

// Hash sensitive data
export function hashData(data: string, salt?: string): string {
  const actualSalt = salt || crypto.randomBytes(16).toString('hex')
  const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512')
  return `${actualSalt}:${hash.toString('hex')}`
}

// Verify hashed data
export function verifyHashedData(data: string, hashedData: string): boolean {
  try {
    const [salt, hash] = hashedData.split(':')
    const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512')
    return hash === verifyHash.toString('hex')
  } catch (error) {
    return false
  }
}

// Encrypt sensitive data
export function encryptData(data: string, key?: string): string {
  const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production'
  const algorithm = 'aes-256-gcm'
  const iv = crypto.randomBytes(16)
  
  const cipher = crypto.createCipher(algorithm, encryptionKey)
  let encrypted = cipher.update(data, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  
  return `${iv.toString('hex')}:${encrypted}`
}

// Decrypt sensitive data
export function decryptData(encryptedData: string, key?: string): string {
  try {
    const encryptionKey = key || process.env.ENCRYPTION_KEY || 'default-key-change-in-production'
    const algorithm = 'aes-256-gcm'
    const [ivHex, encrypted] = encryptedData.split(':')
    const iv = Buffer.from(ivHex, 'hex')
    
    const decipher = crypto.createDecipher(algorithm, encryptionKey)
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  } catch (error) {
    throw new Error('Failed to decrypt data')
  }
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validate phone number (basic international format)
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

// Validate URL
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Check for suspicious patterns
export function detectSuspiciousActivity(request: NextRequest): {
  suspicious: boolean
  reasons: string[]
} {
  const reasons: string[] = []
  const userAgent = request.headers.get('user-agent') || ''
  const referer = request.headers.get('referer') || ''
  
  // Check for bot-like user agents
  const botPatterns = [
    /bot/i, /crawler/i, /spider/i, /scraper/i,
    /curl/i, /wget/i, /python/i, /php/i
  ]
  
  if (botPatterns.some(pattern => pattern.test(userAgent))) {
    reasons.push('Bot-like user agent detected')
  }
  
  // Check for missing or suspicious referer
  if (request.method === 'POST' && !referer) {
    reasons.push('Missing referer on POST request')
  }
  
  // Check for suspicious headers
  const suspiciousHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-cluster-client-ip'
  ]
  
  suspiciousHeaders.forEach(header => {
    const value = request.headers.get(header)
    if (value && value.split(',').length > 3) {
      reasons.push(`Suspicious ${header} header with multiple IPs`)
    }
  })
  
  return {
    suspicious: reasons.length > 0,
    reasons
  }
}

// Rate limiting helper
export class RateLimiter {
  private store = new Map<string, { count: number; resetTime: number }>()
  
  constructor(
    private maxRequests: number = 100,
    private windowMs: number = 15 * 60 * 1000 // 15 minutes
  ) {}
  
  isAllowed(identifier: string): { allowed: boolean; resetTime?: number } {
    const now = Date.now()
    
    // Clean expired entries
    for (const [key, value] of this.store.entries()) {
      if (value.resetTime < now) {
        this.store.delete(key)
      }
    }
    
    const current = this.store.get(identifier) || { 
      count: 0, 
      resetTime: now + this.windowMs 
    }
    
    if (current.resetTime < now) {
      current.count = 1
      current.resetTime = now + this.windowMs
    } else {
      current.count++
    }
    
    this.store.set(identifier, current)
    
    return {
      allowed: current.count <= this.maxRequests,
      resetTime: current.resetTime
    }
  }
  
  getRemainingRequests(identifier: string): number {
    const current = this.store.get(identifier)
    if (!current) return this.maxRequests
    
    return Math.max(0, this.maxRequests - current.count)
  }
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  valid: boolean
  score: number
  feedback: string[]
} {
  const feedback: string[] = []
  let score = 0
  
  if (password.length < 8) {
    feedback.push('Password must be at least 8 characters long')
  } else {
    score += 1
  }
  
  if (!/[a-z]/.test(password)) {
    feedback.push('Password must contain at least one lowercase letter')
  } else {
    score += 1
  }
  
  if (!/[A-Z]/.test(password)) {
    feedback.push('Password must contain at least one uppercase letter')
  } else {
    score += 1
  }
  
  if (!/\d/.test(password)) {
    feedback.push('Password must contain at least one number')
  } else {
    score += 1
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    feedback.push('Password must contain at least one special character')
  } else {
    score += 1
  }
  
  if (password.length >= 12) {
    score += 1
  }
  
  return {
    valid: score >= 4,
    score,
    feedback
  }
}

// CSRF token generation and validation
export class CSRFProtection {
  private static secret = process.env.CSRF_SECRET || 'default-csrf-secret'
  
  static generateToken(sessionId: string): string {
    const timestamp = Date.now().toString()
    const data = `${sessionId}:${timestamp}`
    const signature = crypto
      .createHmac('sha256', this.secret)
      .update(data)
      .digest('hex')
    
    return Buffer.from(`${data}:${signature}`).toString('base64')
  }
  
  static validateToken(token: string, sessionId: string): boolean {
    try {
      const decoded = Buffer.from(token, 'base64').toString('utf8')
      const [receivedSessionId, timestamp, signature] = decoded.split(':')
      
      if (receivedSessionId !== sessionId) {
        return false
      }
      
      // Check if token is not older than 1 hour
      const tokenAge = Date.now() - parseInt(timestamp)
      if (tokenAge > 60 * 60 * 1000) {
        return false
      }
      
      const data = `${receivedSessionId}:${timestamp}`
      const expectedSignature = crypto
        .createHmac('sha256', this.secret)
        .update(data)
        .digest('hex')
      
      return signature === expectedSignature
    } catch {
      return false
    }
  }
}

// IP address utilities
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const clientIP = request.ip
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return clientIP || 'unknown'
}

// Check if IP is in allowed range (basic CIDR check)
export function isIPAllowed(ip: string, allowedRanges: string[]): boolean {
  if (allowedRanges.length === 0) return true
  
  // Simple implementation - in production, use a proper CIDR library
  return allowedRanges.some(range => {
    if (range === ip) return true
    if (range.includes('/')) {
      // Basic CIDR check (simplified)
      const [network, prefix] = range.split('/')
      const networkParts = network.split('.').map(Number)
      const ipParts = ip.split('.').map(Number)
      
      // This is a very basic implementation
      // In production, use a proper CIDR library
      return networkParts[0] === ipParts[0] && networkParts[1] === ipParts[1]
    }
    return false
  })
}
