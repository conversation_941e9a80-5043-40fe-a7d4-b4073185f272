import { db } from '@/lib/db'
import { getClientIP } from '@/lib/security-utils'
import { NextRequest } from 'next/server'

export interface AuditLogEntry {
  action: string
  resource: string
  resourceId?: string
  userId?: string
  organizationId?: string
  ipAddress?: string
  userAgent?: string
  details?: Record<string, any>
  success: boolean
  errorMessage?: string
}

export class AuditLogger {
  // Log an audit event
  static async log(entry: AuditLogEntry): Promise<void> {
    try {
      // In a production environment, you might want to:
      // 1. Store in a separate audit database
      // 2. Send to a logging service (e.g., CloudWatch, Datadog)
      // 3. Write to files for compliance
      
      console.log('AUDIT LOG:', {
        timestamp: new Date().toISOString(),
        ...entry
      })
      
      // For now, we'll store in the database
      // In production, consider using a separate audit table or service
      await db.activity.create({
        data: {
          type: 'AUDIT',
          title: `${entry.action} ${entry.resource}`,
          description: entry.errorMessage || `${entry.action} performed on ${entry.resource}`,
          completed: entry.success,
          organizationId: entry.organizationId || '',
          userId: entry.userId,
          metadata: JSON.stringify({
            action: entry.action,
            resource: entry.resource,
            resourceId: entry.resourceId,
            ipAddress: entry.ipAddress,
            userAgent: entry.userAgent,
            details: entry.details,
            success: entry.success,
            errorMessage: entry.errorMessage
          })
        }
      })
    } catch (error) {
      // Don't let audit logging failures break the application
      console.error('Failed to write audit log:', error)
    }
  }

  // Log authentication events
  static async logAuth(
    action: 'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED' | 'REGISTER' | 'PASSWORD_RESET',
    userId?: string,
    organizationId?: string,
    request?: NextRequest,
    details?: Record<string, any>
  ): Promise<void> {
    await this.log({
      action,
      resource: 'AUTH',
      userId,
      organizationId,
      ipAddress: request ? getClientIP(request) : undefined,
      userAgent: request?.headers.get('user-agent') || undefined,
      details,
      success: !action.includes('FAILED')
    })
  }

  // Log data access events
  static async logDataAccess(
    action: 'READ' | 'CREATE' | 'UPDATE' | 'DELETE',
    resource: string,
    resourceId: string,
    userId: string,
    organizationId: string,
    request?: NextRequest,
    success: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      action,
      resource,
      resourceId,
      userId,
      organizationId,
      ipAddress: request ? getClientIP(request) : undefined,
      userAgent: request?.headers.get('user-agent') || undefined,
      success,
      errorMessage
    })
  }

  // Log security events
  static async logSecurity(
    action: 'RATE_LIMIT_EXCEEDED' | 'SUSPICIOUS_ACTIVITY' | 'UNAUTHORIZED_ACCESS' | 'PERMISSION_DENIED',
    details: Record<string, any>,
    request?: NextRequest,
    userId?: string,
    organizationId?: string
  ): Promise<void> {
    await this.log({
      action,
      resource: 'SECURITY',
      userId,
      organizationId,
      ipAddress: request ? getClientIP(request) : undefined,
      userAgent: request?.headers.get('user-agent') || undefined,
      details,
      success: false
    })
  }

  // Log system events
  static async logSystem(
    action: string,
    details: Record<string, any>,
    success: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      action,
      resource: 'SYSTEM',
      details,
      success,
      errorMessage
    })
  }

  // Get audit logs for an organization
  static async getAuditLogs(
    organizationId: string,
    filters: {
      action?: string
      resource?: string
      userId?: string
      startDate?: Date
      endDate?: Date
      limit?: number
    } = {}
  ): Promise<any[]> {
    const where: any = {
      organizationId,
      type: 'AUDIT'
    }

    if (filters.startDate || filters.endDate) {
      where.createdAt = {}
      if (filters.startDate) where.createdAt.gte = filters.startDate
      if (filters.endDate) where.createdAt.lte = filters.endDate
    }

    if (filters.userId) {
      where.userId = filters.userId
    }

    const activities = await db.activity.findMany({
      where,
      include: {
        user: {
          select: { name: true, email: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: filters.limit || 100
    })

    return activities.map(activity => {
      const metadata = activity.metadata ? JSON.parse(activity.metadata) : {}
      return {
        id: activity.id,
        timestamp: activity.createdAt,
        action: metadata.action,
        resource: metadata.resource,
        resourceId: metadata.resourceId,
        user: activity.user,
        ipAddress: metadata.ipAddress,
        userAgent: metadata.userAgent,
        details: metadata.details,
        success: metadata.success,
        errorMessage: metadata.errorMessage
      }
    })
  }

  // Generate audit report
  static async generateAuditReport(
    organizationId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{
    summary: Record<string, number>
    events: any[]
    securityEvents: any[]
    failedActions: any[]
  }> {
    const logs = await this.getAuditLogs(organizationId, {
      startDate,
      endDate,
      limit: 1000
    })

    const summary: Record<string, number> = {}
    const securityEvents: any[] = []
    const failedActions: any[] = []

    logs.forEach(log => {
      // Count actions
      summary[log.action] = (summary[log.action] || 0) + 1

      // Collect security events
      if (log.resource === 'SECURITY') {
        securityEvents.push(log)
      }

      // Collect failed actions
      if (!log.success) {
        failedActions.push(log)
      }
    })

    return {
      summary,
      events: logs,
      securityEvents,
      failedActions
    }
  }
}

// Middleware wrapper for automatic audit logging
export function withAuditLogging(
  action: string,
  resource: string,
  getResourceId?: (req: NextRequest) => string
) {
  return function (handler: Function) {
    return async function (req: NextRequest, ...args: any[]) {
      const startTime = Date.now()
      let success = true
      let errorMessage: string | undefined

      try {
        const result = await handler(req, ...args)
        
        // Log successful action
        const session = (req as any).user // Assuming user is attached by auth middleware
        if (session) {
          await AuditLogger.logDataAccess(
            action as any,
            resource,
            getResourceId ? getResourceId(req) : 'unknown',
            session.id,
            session.organizationId,
            req,
            true
          )
        }

        return result
      } catch (error) {
        success = false
        errorMessage = error instanceof Error ? error.message : 'Unknown error'
        
        // Log failed action
        const session = (req as any).user
        if (session) {
          await AuditLogger.logDataAccess(
            action as any,
            resource,
            getResourceId ? getResourceId(req) : 'unknown',
            session.id,
            session.organizationId,
            req,
            false,
            errorMessage
          )
        }

        throw error
      } finally {
        const duration = Date.now() - startTime
        console.log(`${action} ${resource} completed in ${duration}ms - Success: ${success}`)
      }
    }
  }
}

// Decorator for audit logging (if using TypeScript decorators)
export function AuditLog(action: string, resource: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now()
      let success = true
      let errorMessage: string | undefined

      try {
        const result = await method.apply(this, args)
        
        // Extract request and session from context if available
        const req = args.find(arg => arg && arg.headers) as NextRequest
        const session = args.find(arg => arg && arg.user)?.user

        if (session && req) {
          await AuditLogger.logDataAccess(
            action as any,
            resource,
            'method-call',
            session.id,
            session.organizationId,
            req,
            true
          )
        }

        return result
      } catch (error) {
        success = false
        errorMessage = error instanceof Error ? error.message : 'Unknown error'
        
        const req = args.find(arg => arg && arg.headers) as NextRequest
        const session = args.find(arg => arg && arg.user)?.user

        if (session && req) {
          await AuditLogger.logDataAccess(
            action as any,
            resource,
            'method-call',
            session.id,
            session.organizationId,
            req,
            false,
            errorMessage
          )
        }

        throw error
      } finally {
        const duration = Date.now() - startTime
        console.log(`${action} ${resource} (${propertyName}) completed in ${duration}ms - Success: ${success}`)
      }
    }

    return descriptor
  }
}
