import { db } from '@/lib/db'
import ZAI from 'z-ai-web-dev-sdk'

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: Date
  type?: 'text' | 'suggestion' | 'quote'
  metadata?: any
}

export interface AIResponse {
  content: string
  type: 'text' | 'suggestion' | 'quote'
  metadata: any
  tokensUsed?: number
  model?: string
}

// Initialize AI service
export async function initializeAI(): Promise<any> {
  try {
    return await ZAI.create()
  } catch (error) {
    console.error('Failed to initialize AI service:', error)
    return null
  }
}

// Generate system prompt based on user context
export function generateSystemPrompt(user: any, organization: any): string {
  return `Je bent een professionele AI assistent voor Quote.AI+CRM, een platform voor het genereren van offertes en klantrelatiebeheer in de bouwsector.

Je taken omvatten:
1. Offertes genereren met prijscalculaties
2. Projectadvies geven over bouwprojecten
3. Projectplanning maken
4. <PERSON><PERSON>e bouwprijzen en materiaalkosten delen
5. <PERSON><PERSON> met materiaalkeuzes en technische specificaties
6. CRM-gerelateerde vragen beantwoorden

Belangrijke richtlijnen:
- Wees professioneel, vriendelijk en behulpzaam
- Geef gedetailleerde en praktische antwoorden
- Vermijd specifieke prijzen tenzij er duidelijke context is
- Wees transparant over schattingen en variabelen
- Geef veiligheidsadvies waar relevant
- Houd rekening met Nederlandse bouwvoorschriften
- Gebruik Nederlandse BTW-tarieven (21% standaard, 9% voor bepaalde diensten)

Context informatie:
- Gebruiker: ${user.name} (${user.role})
- Organisatie: ${organization?.name || 'Onbekend'}
- Plan: ${organization?.plan || 'FREE'}

Gebruik Nederlands in je antwoorden tenzij specifiek gevraagd om Engels.`
}

// Get or create AI conversation
export async function getOrCreateConversation(
  conversationId: string | null,
  userId: string,
  organizationId: string,
  title?: string
): Promise<any> {
  if (conversationId) {
    // Try to find existing conversation
    const existing = await db.aiConversation.findFirst({
      where: {
        id: conversationId,
        userId,
        organizationId
      }
    })
    
    if (existing) {
      return existing
    }
  }

  // Create new conversation
  return await db.aiConversation.create({
    data: {
      title: title || 'New Conversation',
      messages: JSON.stringify([]),
      organizationId,
      userId
    }
  })
}

// Add message to conversation
export async function addMessageToConversation(
  conversationId: string,
  message: ChatMessage
): Promise<void> {
  const conversation = await db.aiConversation.findUnique({
    where: { id: conversationId }
  })

  if (!conversation) {
    throw new Error('Conversation not found')
  }

  const messages = JSON.parse(conversation.messages) as ChatMessage[]
  messages.push({
    ...message,
    timestamp: new Date()
  })

  await db.aiConversation.update({
    where: { id: conversationId },
    data: {
      messages: JSON.stringify(messages),
      updatedAt: new Date()
    }
  })
}

// Get AI response
export async function getAIResponse(
  messages: ChatMessage[],
  context?: any
): Promise<AIResponse> {
  const ai = await initializeAI()
  
  if (!ai) {
    return getFallbackResponse(messages[messages.length - 1]?.content || '')
  }

  try {
    const completion = await ai.chat.completions.create({
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: 0.7,
      max_tokens: 1000
    })

    const content = completion.choices[0]?.message?.content || 'Excuses, ik kon geen antwoord genereren.'
    const tokensUsed = completion.usage?.total_tokens || 0
    const model = completion.model || 'unknown'

    // Analyze response type
    const { type, metadata } = analyzeResponseType(content)

    return {
      content,
      type,
      metadata,
      tokensUsed,
      model
    }
  } catch (error) {
    console.error('AI API Error:', error)
    return getFallbackResponse(messages[messages.length - 1]?.content || '')
  }
}

// Analyze response type based on content
function analyzeResponseType(content: string): { type: 'text' | 'suggestion' | 'quote', metadata: any } {
  const lowerContent = content.toLowerCase()
  let type: 'text' | 'suggestion' | 'quote' = 'text'
  let metadata: any = {}

  // Check for quote-related content
  if (lowerContent.includes('offerte') || lowerContent.includes('prijs') || lowerContent.includes('€') || lowerContent.includes('euro')) {
    type = 'quote'
    
    // Extract pricing information
    const priceMatches = content.match(/€[\d,.]+/g)
    if (priceMatches) {
      metadata.prices = priceMatches.map(price => 
        parseFloat(price.replace('€', '').replace(',', '.'))
      )
    }

    // Extract project details
    const projectKeywords = ['m²', 'vierkante meter', 'uur', 'dag', 'week']
    metadata.hasProjectDetails = projectKeywords.some(keyword => 
      lowerContent.includes(keyword)
    )
  }
  
  // Check for suggestion-related content
  if (lowerContent.includes('suggestie') || lowerContent.includes('advies') || 
      lowerContent.includes('tip') || lowerContent.includes('aanbeveling')) {
    type = 'suggestion'
    
    // Extract suggestion categories
    if (lowerContent.includes('materiaal')) metadata.category = 'materials'
    if (lowerContent.includes('planning')) metadata.category = 'planning'
    if (lowerContent.includes('veiligheid')) metadata.category = 'safety'
    if (lowerContent.includes('kosten')) metadata.category = 'costs'
  }

  return { type, metadata }
}

// Generate fallback response when AI is unavailable
function getFallbackResponse(userMessage: string): AIResponse {
  const lowerMessage = userMessage.toLowerCase()
  
  if (lowerMessage.includes('offerte') || lowerMessage.includes('prijs') || lowerMessage.includes('kosten')) {
    return {
      content: `Ik kan je helpen met het maken van een offerte. Voor een nauwkeurige offerte heb ik meer informatie nodig over:

- Type project (renovatie, nieuwbouw, onderhoud)
- Oppervlakte in m²
- Gewenste materialen en afwerking
- Tijdsplanning

Wil je deze informatie delen zodat ik een betere schatting kan maken?`,
      type: 'quote',
      metadata: { fallback: true }
    }
  }

  if (lowerMessage.includes('planning') || lowerMessage.includes('project')) {
    return {
      content: `Voor projectplanning adviseer ik de volgende stappen:

1. **Voorbereiding** (1-2 weken)
   - Vergunningen aanvragen
   - Materialen bestellen
   - Planning maken

2. **Uitvoering** (afhankelijk van project)
   - Demontage/voorbereiding
   - Hoofdwerkzaamheden
   - Afwerking

3. **Oplevering** (1 week)
   - Controle en nazorg
   - Documentatie

Wil je een specifiekere planning voor jouw project?`,
      type: 'suggestion',
      metadata: { fallback: true, category: 'planning' }
    }
  }

  return {
    content: `Ik help je graag! Ik kan assisteren met:

- **Offertes maken** met prijscalculaties
- **Projectadvies** en materiaalkeuzes  
- **Planning** en tijdschema's
- **Actuele prijzen** voor bouwmaterialen
- **CRM-vragen** over klanten en projecten

Wat kan ik voor je doen?`,
    type: 'text',
    metadata: { fallback: true }
  }
}

// Extract actionable items from AI response
export function extractActionableItems(content: string): string[] {
  const actions: string[] = []
  const lines = content.split('\n')
  
  for (const line of lines) {
    const trimmed = line.trim()
    // Look for action items (lines starting with -, *, or numbers)
    if (trimmed.match(/^[-*•]\s+/) || trimmed.match(/^\d+\.\s+/)) {
      actions.push(trimmed.replace(/^[-*•]\s+/, '').replace(/^\d+\.\s+/, ''))
    }
  }
  
  return actions
}

// Generate conversation title from first message
export function generateConversationTitle(message: string): string {
  const words = message.split(' ').slice(0, 6)
  let title = words.join(' ')
  
  if (title.length > 50) {
    title = title.substring(0, 47) + '...'
  }
  
  return title || 'New Conversation'
}
