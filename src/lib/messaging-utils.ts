import { db } from '@/lib/db'
import nodemailer from 'nodemailer'

export interface EmailConfig {
  host: string
  port: number
  secure?: boolean
  auth: {
    user: string
    pass: string
  }
  from: string
}

export interface WhatsAppConfig {
  token: string
  phoneId: string
  businessId: string
  webhookVerifyToken: string
}

export interface MessageData {
  type: 'EMAIL' | 'WHATSAPP' | 'SMS'
  direction: 'INBOUND' | 'OUTBOUND'
  subject?: string
  content: string
  fromAddress?: string
  toAddress?: string
  phoneNumber?: string
  customerId?: string
  quoteId?: string
  metadata?: any
}

// Email service
export class EmailService {
  private transporter: any
  private config: EmailConfig

  constructor(config: EmailConfig) {
    this.config = config
    this.transporter = nodemailer.createTransporter({
      host: config.host,
      port: config.port,
      secure: config.port === 465,
      auth: config.auth
    })
  }

  async sendEmail(
    to: string | string[],
    subject: string,
    content: string,
    options: {
      html?: string
      attachments?: any[]
      cc?: string[]
      bcc?: string[]
    } = {}
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const mailOptions = {
        from: this.config.from,
        to: Array.isArray(to) ? to.join(', ') : to,
        subject,
        text: content,
        html: options.html || content,
        cc: options.cc?.join(', '),
        bcc: options.bcc?.join(', '),
        attachments: options.attachments
      }

      const result = await this.transporter.sendMail(mailOptions)
      
      return {
        success: true,
        messageId: result.messageId
      }
    } catch (error) {
      console.error('Email send error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify()
      return true
    } catch (error) {
      console.error('Email connection verification failed:', error)
      return false
    }
  }
}

// WhatsApp service
export class WhatsAppService {
  private config: WhatsAppConfig
  private baseUrl = 'https://graph.facebook.com/v18.0'

  constructor(config: WhatsAppConfig) {
    this.config = config
  }

  async sendMessage(
    to: string,
    message: string,
    type: 'text' | 'template' = 'text'
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const url = `${this.baseUrl}/${this.config.phoneId}/messages`
      
      const payload = {
        messaging_product: 'whatsapp',
        to: to.replace(/\D/g, ''), // Remove non-digits
        type: type,
        text: {
          body: message
        }
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (response.ok && result.messages) {
        return {
          success: true,
          messageId: result.messages[0].id
        }
      } else {
        return {
          success: false,
          error: result.error?.message || 'Failed to send WhatsApp message'
        }
      }
    } catch (error) {
      console.error('WhatsApp send error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async sendTemplate(
    to: string,
    templateName: string,
    parameters: any[] = []
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const url = `${this.baseUrl}/${this.config.phoneId}/messages`
      
      const payload = {
        messaging_product: 'whatsapp',
        to: to.replace(/\D/g, ''),
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: 'nl'
          },
          components: parameters.length > 0 ? [{
            type: 'body',
            parameters: parameters.map(param => ({
              type: 'text',
              text: param
            }))
          }] : []
        }
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (response.ok && result.messages) {
        return {
          success: true,
          messageId: result.messages[0].id
        }
      } else {
        return {
          success: false,
          error: result.error?.message || 'Failed to send WhatsApp template'
        }
      }
    } catch (error) {
      console.error('WhatsApp template send error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  verifyWebhook(mode: string, token: string, challenge: string): string | null {
    if (mode === 'subscribe' && token === this.config.webhookVerifyToken) {
      return challenge
    }
    return null
  }

  parseWebhookMessage(body: any): any[] {
    const messages: any[] = []
    
    if (body.entry) {
      for (const entry of body.entry) {
        if (entry.changes) {
          for (const change of entry.changes) {
            if (change.value && change.value.messages) {
              for (const message of change.value.messages) {
                messages.push({
                  id: message.id,
                  from: message.from,
                  timestamp: new Date(parseInt(message.timestamp) * 1000),
                  type: message.type,
                  text: message.text?.body || '',
                  contacts: change.value.contacts
                })
              }
            }
          }
        }
      }
    }
    
    return messages
  }
}

// Message logging service
export class MessageLogService {
  static async logMessage(
    organizationId: string,
    messageData: MessageData,
    userId?: string
  ): Promise<any> {
    return await db.messageLog.create({
      data: {
        type: messageData.type,
        direction: messageData.direction,
        status: 'SENT',
        subject: messageData.subject,
        content: messageData.content,
        fromAddress: messageData.fromAddress,
        toAddress: messageData.toAddress,
        phoneNumber: messageData.phoneNumber,
        metadata: messageData.metadata ? JSON.stringify(messageData.metadata) : null,
        organizationId,
        customerId: messageData.customerId,
        quoteId: messageData.quoteId,
        userId
      }
    })
  }

  static async updateMessageStatus(
    messageId: string,
    status: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED' | 'READ'
  ): Promise<void> {
    await db.messageLog.update({
      where: { id: messageId },
      data: { status }
    })
  }

  static async getMessageHistory(
    organizationId: string,
    filters: {
      customerId?: string
      quoteId?: string
      type?: 'EMAIL' | 'WHATSAPP' | 'SMS'
      limit?: number
    } = {}
  ): Promise<any[]> {
    const where: any = { organizationId }
    
    if (filters.customerId) where.customerId = filters.customerId
    if (filters.quoteId) where.quoteId = filters.quoteId
    if (filters.type) where.type = filters.type

    return await db.messageLog.findMany({
      where,
      include: {
        customer: {
          select: { name: true, company: true }
        },
        quote: {
          select: { number: true, title: true }
        },
        user: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: filters.limit || 50
    })
  }
}

// Get messaging configuration for organization
export async function getMessagingConfig(organizationId: string): Promise<{
  email?: EmailConfig
  whatsapp?: WhatsAppConfig
}> {
  const organization = await db.organization.findUnique({
    where: { id: organizationId },
    select: {
      smtpHost: true,
      smtpPort: true,
      smtpUsername: true,
      smtpPassword: true,
      emailFromAddress: true,
      whatsappToken: true,
      whatsappPhoneId: true,
      whatsappBusinessId: true
    }
  })

  if (!organization) {
    throw new Error('Organization not found')
  }

  const config: any = {}

  // Email configuration
  if (organization.smtpHost && organization.smtpUsername && organization.smtpPassword) {
    config.email = {
      host: organization.smtpHost,
      port: organization.smtpPort || 587,
      auth: {
        user: organization.smtpUsername,
        pass: organization.smtpPassword
      },
      from: organization.emailFromAddress || organization.smtpUsername
    }
  }

  // WhatsApp configuration
  if (organization.whatsappToken && organization.whatsappPhoneId) {
    config.whatsapp = {
      token: organization.whatsappToken,
      phoneId: organization.whatsappPhoneId,
      businessId: organization.whatsappBusinessId || '',
      webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'default-verify-token'
    }
  }

  return config
}
