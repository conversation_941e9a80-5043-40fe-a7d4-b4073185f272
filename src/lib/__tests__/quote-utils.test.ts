import {
  calculateQuoteTotals,
  processQuoteItems,
  generateExpiryDate,
  isQuoteExpired,
  getQuoteStatus,
  formatQuoteNumber,
  isValidStatusTransition
} from '../quote-utils'

describe('Quote Utils', () => {
  describe('calculateQuoteTotals', () => {
    it('should calculate totals correctly', () => {
      const items = [
        { beschrijving: 'Item 1', aantal: 2, eenheid: 'stuk', eenheidPrijs: 100 },
        { beschrijving: 'Item 2', aantal: 1, eenheid: 'stuk', eenheidPrijs: 50 }
      ]

      const result = calculateQuoteTotals(items, 21)

      expect(result.basisPrijs).toBe(250) // (2 * 100) + (1 * 50)
      expect(result.btwPercentage).toBe(21)
      expect(result.btwBedrag).toBe(52.5) // 250 * 0.21
      expect(result.totaalPrijs).toBe(302.5) // 250 + 52.5
    })

    it('should handle empty items array', () => {
      const result = calculateQuoteTotals([], 21)

      expect(result.basisPrijs).toBe(0)
      expect(result.btwPercentage).toBe(21)
      expect(result.btwBedrag).toBe(0)
      expect(result.totaalPrijs).toBe(0)
    })

    it('should round to 2 decimal places', () => {
      const items = [
        { beschrijving: 'Item 1', aantal: 3, eenheid: 'stuk', eenheidPrijs: 33.33 }
      ]

      const result = calculateQuoteTotals(items, 21)

      expect(result.basisPrijs).toBe(99.99)
      expect(result.btwBedrag).toBe(21) // 99.99 * 0.21 = 20.9979, rounded to 21
      expect(result.totaalPrijs).toBe(120.99)
    })
  })

  describe('processQuoteItems', () => {
    it('should process valid items correctly', () => {
      const items = [
        { beschrijving: 'Item 1', aantal: 2, eenheid: 'stuk', eenheidPrijs: 100 },
        { beschrijving: 'Item 2', aantal: 1, eenheidPrijs: 50 }
      ]

      const result = processQuoteItems(items)

      expect(result.items).toHaveLength(2)
      expect(result.items[0]).toEqual({
        beschrijving: 'Item 1',
        aantal: 2,
        eenheid: 'stuk',
        eenheidPrijs: 100,
        volgorde: 1
      })
      expect(result.items[1]).toEqual({
        beschrijving: 'Item 2',
        aantal: 1,
        eenheid: 'stuk', // Default value
        eenheidPrijs: 50,
        volgorde: 2
      })
      expect(result.totals.basisPrijs).toBe(250)
    })

    it('should handle invalid values', () => {
      const items = [
        { beschrijving: '', aantal: -1, eenheidPrijs: -50 },
        { aantal: 'invalid', eenheidPrijs: 'invalid' }
      ]

      const result = processQuoteItems(items)

      expect(result.items).toHaveLength(2)
      expect(result.items[0]).toEqual({
        beschrijving: '',
        aantal: 1, // Minimum 1
        eenheid: 'stuk',
        eenheidPrijs: 0, // Minimum 0
        volgorde: 1
      })
      expect(result.items[1]).toEqual({
        beschrijving: '',
        aantal: 1,
        eenheid: 'stuk',
        eenheidPrijs: 0,
        volgorde: 2
      })
    })
  })

  describe('generateExpiryDate', () => {
    it('should generate date 30 days from now by default', () => {
      const result = generateExpiryDate()
      const expected = new Date()
      expected.setDate(expected.getDate() + 30)

      expect(result.getDate()).toBe(expected.getDate())
      expect(result.getMonth()).toBe(expected.getMonth())
      expect(result.getFullYear()).toBe(expected.getFullYear())
    })

    it('should generate date with custom days', () => {
      const result = generateExpiryDate(7)
      const expected = new Date()
      expected.setDate(expected.getDate() + 7)

      expect(result.getDate()).toBe(expected.getDate())
    })
  })

  describe('isQuoteExpired', () => {
    it('should return false for null expiry date', () => {
      expect(isQuoteExpired(null)).toBe(false)
    })

    it('should return true for past date', () => {
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 1)
      
      expect(isQuoteExpired(pastDate)).toBe(true)
    })

    it('should return false for future date', () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 1)
      
      expect(isQuoteExpired(futureDate)).toBe(false)
    })
  })

  describe('getQuoteStatus', () => {
    it('should return ACCEPTED for accepted quotes', () => {
      expect(getQuoteStatus('ACCEPTED', null, null)).toBe('ACCEPTED')
    })

    it('should return REJECTED for rejected quotes', () => {
      expect(getQuoteStatus('REJECTED', null, null)).toBe('REJECTED')
    })

    it('should return EXPIRED for expired quotes', () => {
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 1)
      
      expect(getQuoteStatus('SENT', pastDate, new Date())).toBe('EXPIRED')
    })

    it('should return SENT for draft quotes that have been sent', () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 1)
      
      expect(getQuoteStatus('DRAFT', futureDate, new Date())).toBe('SENT')
    })

    it('should return original status for non-expired drafts', () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 1)
      
      expect(getQuoteStatus('DRAFT', futureDate, null)).toBe('DRAFT')
    })
  })

  describe('formatQuoteNumber', () => {
    it('should format quote number correctly', () => {
      expect(formatQuoteNumber('QUOTE-202401-001')).toBe('#202401-001')
    })

    it('should handle numbers without QUOTE prefix', () => {
      expect(formatQuoteNumber('202401-001')).toBe('#202401-001')
    })
  })

  describe('isValidStatusTransition', () => {
    it('should allow valid transitions from DRAFT', () => {
      expect(isValidStatusTransition('DRAFT', 'SENT')).toBe(true)
      expect(isValidStatusTransition('DRAFT', 'REJECTED')).toBe(true)
      expect(isValidStatusTransition('DRAFT', 'ACCEPTED')).toBe(false)
    })

    it('should allow valid transitions from SENT', () => {
      expect(isValidStatusTransition('SENT', 'ACCEPTED')).toBe(true)
      expect(isValidStatusTransition('SENT', 'REJECTED')).toBe(true)
      expect(isValidStatusTransition('SENT', 'EXPIRED')).toBe(true)
      expect(isValidStatusTransition('SENT', 'DRAFT')).toBe(false)
    })

    it('should not allow transitions from ACCEPTED', () => {
      expect(isValidStatusTransition('ACCEPTED', 'REJECTED')).toBe(false)
      expect(isValidStatusTransition('ACCEPTED', 'DRAFT')).toBe(false)
    })

    it('should allow reopening from REJECTED and EXPIRED', () => {
      expect(isValidStatusTransition('REJECTED', 'DRAFT')).toBe(true)
      expect(isValidStatusTransition('EXPIRED', 'DRAFT')).toBe(true)
    })

    it('should return false for invalid current status', () => {
      expect(isValidStatusTransition('INVALID', 'SENT')).toBe(false)
    })
  })
})
