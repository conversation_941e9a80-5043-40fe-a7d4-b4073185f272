'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Search, 
  FileText, 
  Users, 
  Building, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  Filter,
  Download,
  Eye
} from 'lucide-react'
import { DashboardLayout } from '@/components/dashboard/layout'

interface SearchResult {
  id: string
  type: 'QUOTE' | 'CUSTOMER' | 'PROPERTY' | 'MESSAGE'
  title: string
  description: string
  customerName?: string
  status?: string
  createdAt: string
  relevanceScore: number
  metadata?: {
    email?: string
    phone?: string
    address?: string
    amount?: number
    propertyType?: string
    sizeM2?: number
  }
}

export default function SearchPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedFilters, setSelectedFilters] = useState<string[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])

  const filterOptions = [
    { id: 'quotes', label: 'Quotes', icon: FileText },
    { id: 'customers', label: 'Customers', icon: Users },
    { id: 'properties', label: 'Properties', icon: Building },
    { id: 'messages', label: 'Messages', icon: Mail }
  ]

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    // Load recent searches from localStorage
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [session, status, router])

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setIsLoading(true)
    
    // Add to recent searches
    const newRecentSearches = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5)
    setRecentSearches(newRecentSearches)
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches))

    try {
      // Mock search results for now
      const mockResults: SearchResult[] = [
        {
          id: '1',
          type: 'QUOTE',
          title: 'Badkamer Renovatie - Jan Jansen',
          description: 'Complete badkamer renovatie inclusief nieuwe tegels, sanitair en verlichting. Geschatte kosten: €8.500',
          customerName: 'Jan Jansen',
          status: 'SENT',
          createdAt: '2024-01-15T10:30:00Z',
          relevanceScore: 0.95,
          metadata: {
            amount: 8500,
            email: '<EMAIL>'
          }
        },
        {
          id: '2',
          type: 'CUSTOMER',
          title: 'Jan Jansen',
          description: 'Klant sinds 2023. Actief met meerdere projecten. 2 properties, 3 quotes.',
          customerName: 'Jan Jansen',
          status: 'ACTIVE',
          createdAt: '2023-06-15T09:00:00Z',
          relevanceScore: 0.90,
          metadata: {
            email: '<EMAIL>',
            phone: '+31 6 12345678',
            address: 'Straatweg 123, Amsterdam'
          }
        },
        {
          id: '3',
          type: 'PROPERTY',
          title: 'Hoofd woning - Jan Jansen',
          description: 'Vrijstaande woning, 150m². Locatie: Straatweg 123, Amsterdam. Type: Woning',
          customerName: 'Jan Jansen',
          status: 'ACTIVE',
          createdAt: '2023-06-15T09:00:00Z',
          relevanceScore: 0.85,
          metadata: {
            address: 'Straatweg 123, Amsterdam',
            propertyType: 'Woning',
            sizeM2: 150
          }
        },
        {
          id: '4',
          type: 'MESSAGE',
          title: 'Offerte aanvraag badkamer renovatie',
          description: 'Geachte heer/mevrouw, ik zou graag een offerte willen ontvangen voor het renoveren van mijn badkamer...',
          customerName: 'Jan Jansen',
          status: 'READ',
          createdAt: '2024-01-15T09:00:00Z',
          relevanceScore: 0.80,
          metadata: {
            email: '<EMAIL>'
          }
        }
      ]

      // Filter results based on selected filters
      let filteredResults = mockResults
      if (selectedFilters.length > 0) {
        filteredResults = mockResults.filter(result => 
          selectedFilters.includes(result.type.toLowerCase())
        )
      }

      setSearchResults(filteredResults)
    } catch (error) {
      console.error('Error searching:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const toggleFilter = (filterId: string) => {
    setSelectedFilters(prev => 
      prev.includes(filterId) 
        ? prev.filter(f => f !== filterId)
        : [...prev, filterId]
    )
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'QUOTE': return FileText
      case 'CUSTOMER': return Users
      case 'PROPERTY': return Building
      case 'MESSAGE': return Mail
      default: return Search
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'QUOTE': return 'bg-purple-500/20 text-purple-400'
      case 'CUSTOMER': return 'bg-green-500/20 text-green-400'
      case 'PROPERTY': return 'bg-blue-500/20 text-blue-400'
      case 'MESSAGE': return 'bg-yellow-500/20 text-yellow-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-500/20 text-green-400'
      case 'SENT': return 'bg-blue-500/20 text-blue-400'
      case 'READ': return 'bg-purple-500/20 text-purple-400'
      case 'DRAFT': return 'bg-yellow-500/20 text-yellow-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const handleResultClick = (result: SearchResult) => {
    switch (result.type) {
      case 'QUOTE':
        router.push(`/quotes/${result.id}`)
        break
      case 'CUSTOMER':
        router.push(`/customers/${result.id}`)
        break
      case 'PROPERTY':
        router.push(`/properties/${result.id}`)
        break
      case 'MESSAGE':
        router.push('/messaging')
        break
    }
  }

  if (status === 'loading') {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
              <Search className="h-8 w-8 text-white" />
            </div>
            <p className="text-white/60">Loading search...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">Search</h1>
            <p className="text-white/60 mt-1">
              Search across all your quotes, customers, properties, and messages
            </p>
          </div>
        </div>

        {/* Search Interface */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Global Search</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-5 w-5" />
                <Input
                  placeholder="Search quotes, customers, properties, messages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="ai-chat-input pl-12 text-lg"
                />
                <Button 
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 gradient-bg"
                  onClick={handleSearch}
                  disabled={isLoading}
                >
                  {isLoading ? 'Searching...' : 'Search'}
                </Button>
              </div>

              {/* Filters */}
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-white/60" />
                <span className="text-sm text-white/60">Filters:</span>
                {filterOptions.map((option) => {
                  const Icon = option.icon
                  const isSelected = selectedFilters.includes(option.id)
                  return (
                    <Button
                      key={option.id}
                      variant={isSelected ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => toggleFilter(option.id)}
                      className={isSelected ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                    >
                      <Icon className="h-4 w-4 mr-1" />
                      {option.label}
                    </Button>
                  )
                })}
              </div>

              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div>
                  <p className="text-sm text-white/60 mb-2">Recent searches:</p>
                  <div className="flex flex-wrap gap-2">
                    {recentSearches.map((search, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSearchQuery(search)
                          handleSearch()
                        }}
                        className="text-white/60 hover:text-white hover:bg-white/10"
                      >
                        {search}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-white">
                Search Results ({searchResults.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {searchResults.map((result) => {
                  const Icon = getTypeIcon(result.type)
                  return (
                    <div
                      key={result.id}
                      className="p-4 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer"
                      onClick={() => handleResultClick(result)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className={`p-3 rounded-lg ${getTypeColor(result.type)}`}>
                            <Icon className="h-6 w-6" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="text-lg font-semibold text-white">{result.title}</h3>
                              <Badge className={getTypeColor(result.type)}>
                                {result.type}
                              </Badge>
                              {result.status && (
                                <Badge className={getStatusColor(result.status)}>
                                  {result.status}
                                </Badge>
                              )}
                            </div>
                            <p className="text-white/60 mb-2">{result.description}</p>
                            
                            {/* Metadata */}
                            {result.metadata && (
                              <div className="flex flex-wrap gap-4 text-sm text-white/60">
                                {result.metadata.email && (
                                  <div className="flex items-center gap-1">
                                    <Mail className="h-4 w-4" />
                                    <span>{result.metadata.email}</span>
                                  </div>
                                )}
                                {result.metadata.phone && (
                                  <div className="flex items-center gap-1">
                                    <Phone className="h-4 w-4" />
                                    <span>{result.metadata.phone}</span>
                                  </div>
                                )}
                                {result.metadata.address && (
                                  <div className="flex items-center gap-1">
                                    <MapPin className="h-4 w-4" />
                                    <span>{result.metadata.address}</span>
                                  </div>
                                )}
                                {result.metadata.amount && (
                                  <div className="flex items-center gap-1">
                                    <span className="text-green-400">€{result.metadata.amount.toLocaleString()}</span>
                                  </div>
                                )}
                                {result.metadata.sizeM2 && (
                                  <div className="flex items-center gap-1">
                                    <span>{result.metadata.sizeM2}m²</span>
                                  </div>
                                )}
                              </div>
                            )}
                            
                            <div className="flex items-center gap-2 text-xs text-white/40 mt-2">
                              <Calendar className="h-3 w-3" />
                              <span>{new Date(result.createdAt).toLocaleDateString()}</span>
                              {result.customerName && (
                                <span>• {result.customerName}</span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-white/15"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {searchResults.length === 0 && searchQuery && !isLoading && (
          <Card className="glass-card">
            <CardContent className="text-center py-12">
              <Search className="h-12 w-12 text-white/20 mx-auto mb-4" />
              <p className="text-white/60 mb-4">No results found for "{searchQuery}"</p>
              <p className="text-white/40 text-sm">Try adjusting your search terms or filters</p>
            </CardContent>
          </Card>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Quotes
              </CardTitle>
              <FileText className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">24</div>
              <p className="text-xs text-white/60">Searchable quotes</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Customers
              </CardTitle>
              <Users className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">156</div>
              <p className="text-xs text-white/60">In database</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Properties
              </CardTitle>
              <Building className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">89</div>
              <p className="text-xs text-white/60">Properties listed</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Messages
              </CardTitle>
              <Mail className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">342</div>
              <p className="text-xs text-white/60">Messages archived</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}