'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  FileBarChart, 
  Download, 
  Calendar, 
  Filter,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  FileText,
  BarChart3,
  Users,
  DollarSign,
  TrendingUp,
  Clock,
  CheckCircle
} from 'lucide-react'
import { DashboardLayout } from '@/components/dashboard/layout'

interface Report {
  id: string
  name: string
  description: string
  type: 'FINANCIAL' | 'SALES' | 'CUSTOMER' | 'PROJECT' | 'CUSTOM'
  status: 'GENERATED' | 'GENERATING' | 'SCHEDULED' | 'FAILED'
  format: 'PDF' | 'EXCEL' | 'CSV'
  createdAt: string
  generatedAt?: string
  size?: string
  downloadUrl?: string
  scheduledFor?: string
  parameters: {
    dateRange: { start: string; end: string }
    filters: Record<string, any>
  }
}

interface ReportTemplate {
  id: string
  name: string
  description: string
  type: 'FINANCIAL' | 'SALES' | 'CUSTOMER' | 'PROJECT'
  icon: any
  category: string
  isPopular: boolean
}

export default function ReportingPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [reports, setReports] = useState<Report[]>([])
  const [templates, setTemplates] = useState<ReportTemplate[]>([])
  const [filteredReports, setFilteredReports] = useState<Report[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('ALL')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    loadReports()
    loadTemplates()
  }, [session, status, router])

  useEffect(() => {
    filterReports()
  }, [reports, searchTerm, typeFilter, statusFilter])

  const loadReports = async () => {
    try {
      // Mock reports data
      const mockReports: Report[] = [
        {
          id: '1',
          name: 'Monthly Revenue Report',
          description: 'Comprehensive revenue analysis for January 2024',
          type: 'FINANCIAL',
          status: 'GENERATED',
          format: 'PDF',
          createdAt: '2024-01-31T10:00:00Z',
          generatedAt: '2024-01-31T10:15:00Z',
          size: '2.4 MB',
          downloadUrl: '/reports/monthly-revenue-jan-2024.pdf',
          parameters: {
            dateRange: { start: '2024-01-01', end: '2024-01-31' },
            filters: { includeBreakdown: true }
          }
        },
        {
          id: '2',
          name: 'Customer Acquisition Report',
          description: 'New customers and retention metrics for Q4 2023',
          type: 'CUSTOMER',
          status: 'GENERATED',
          format: 'EXCEL',
          createdAt: '2024-01-15T14:30:00Z',
          generatedAt: '2024-01-15T14:35:00Z',
          size: '1.8 MB',
          downloadUrl: '/reports/customer-acquisition-q4-2023.xlsx',
          parameters: {
            dateRange: { start: '2023-10-01', end: '2023-12-31' },
            filters: { includeChurnAnalysis: true }
          }
        },
        {
          id: '3',
          name: 'Sales Performance Report',
          description: 'Quarterly sales performance and conversion analysis',
          type: 'SALES',
          status: 'GENERATING',
          format: 'PDF',
          createdAt: '2024-01-20T09:00:00Z',
          parameters: {
            dateRange: { start: '2024-01-01', end: '2024-03-31' },
            filters: { includeForecasts: true }
          }
        },
        {
          id: '4',
          name: 'Project Status Report',
          description: 'Current project statuses and completion rates',
          type: 'PROJECT',
          status: 'SCHEDULED',
          format: 'PDF',
          createdAt: '2024-01-25T16:00:00Z',
          scheduledFor: '2024-02-01T09:00:00Z',
          parameters: {
            dateRange: { start: '2024-01-01', end: '2024-01-31' },
            filters: { includeTimeline: true }
          }
        }
      ]

      setReports(mockReports)
    } catch (error) {
      console.error('Error loading reports:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadTemplates = async () => {
    try {
      // Mock templates data
      const mockTemplates: ReportTemplate[] = [
        {
          id: '1',
          name: 'Revenue Summary',
          description: 'Monthly/quarterly revenue breakdown and analysis',
          type: 'FINANCIAL',
          icon: DollarSign,
          category: 'Financial Reports',
          isPopular: true
        },
        {
          id: '2',
          name: 'Sales Pipeline',
          description: 'Current sales pipeline and conversion metrics',
          type: 'SALES',
          icon: TrendingUp,
          category: 'Sales Reports',
          isPopular: true
        },
        {
          id: '3',
          name: 'Customer Analytics',
          description: 'Customer demographics and behavior analysis',
          type: 'CUSTOMER',
          icon: Users,
          category: 'Customer Reports',
          isPopular: false
        },
        {
          id: '4',
          name: 'Project Progress',
          description: 'Project timelines and completion status',
          type: 'PROJECT',
          icon: BarChart3,
          category: 'Project Reports',
          isPopular: false
        },
        {
          id: '5',
          name: 'Quote Performance',
          description: 'Quote generation and acceptance rates',
          type: 'SALES',
          icon: FileText,
          category: 'Sales Reports',
          isPopular: true
        },
        {
          id: '6',
          name: 'Payment Analysis',
          description: 'Payment trends and aging analysis',
          type: 'FINANCIAL',
          icon: DollarSign,
          category: 'Financial Reports',
          isPopular: false
        }
      ]

      setTemplates(mockTemplates)
    } catch (error) {
      console.error('Error loading templates:', error)
    }
  }

  const filterReports = () => {
    let filtered = reports

    if (searchTerm) {
      filtered = filtered.filter(report =>
        report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (typeFilter !== 'ALL') {
      filtered = filtered.filter(report => report.type === typeFilter)
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(report => report.status === statusFilter)
    }

    setFilteredReports(filtered)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'GENERATED': return 'bg-green-500/20 text-green-400'
      case 'GENERATING': return 'bg-blue-500/20 text-blue-400'
      case 'SCHEDULED': return 'bg-yellow-500/20 text-yellow-400'
      case 'FAILED': return 'bg-red-500/20 text-red-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'FINANCIAL': return 'bg-green-500/20 text-green-400'
      case 'SALES': return 'bg-blue-500/20 text-blue-400'
      case 'CUSTOMER': return 'bg-purple-500/20 text-purple-400'
      case 'PROJECT': return 'bg-orange-500/20 text-orange-400'
      case 'CUSTOM': return 'bg-gray-500/20 text-gray-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const handleGenerateReport = (templateId: string) => {
    // Handle report generation
    console.log('Generating report from template:', templateId)
  }

  const handleDownloadReport = (reportId: string) => {
    // Handle report download
    console.log('Downloading report:', reportId)
  }

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
              <FileBarChart className="h-8 w-8 text-white" />
            </div>
            <p className="text-white/60">Loading reports...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">Reporting</h1>
            <p className="text-white/60 mt-1">
              Generate, schedule, and manage business reports
            </p>
          </div>
          <Button 
            className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
            onClick={() => {
              // New report logic
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Report
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Reports
              </CardTitle>
              <FileBarChart className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{reports.length}</div>
              <p className="text-xs text-white/60">Reports generated</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                This Month
              </CardTitle>
              <Calendar className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {reports.filter(r => new Date(r.createdAt).getMonth() === new Date().getMonth()).length}
              </div>
              <p className="text-xs text-white/60">Generated this month</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Scheduled
              </CardTitle>
              <Clock className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {reports.filter(r => r.status === 'SCHEDULED').length}
              </div>
              <p className="text-xs text-white/60">Scheduled reports</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Templates
              </CardTitle>
              <FileText className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{templates.length}</div>
              <p className="text-xs text-white/60">Available templates</p>
            </CardContent>
          </Card>
        </div>

        {/* Report Templates */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Report Templates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => {
                const Icon = template.icon
                return (
                  <div key={template.id} className="p-4 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-200">
                    <div className="flex items-start justify-between mb-3">
                      <div className="p-2 rounded-lg bg-blue-500/20">
                        <Icon className="h-5 w-5 text-blue-400" />
                      </div>
                      {template.isPopular && (
                        <Badge className="bg-yellow-500/20 text-yellow-400 text-xs">
                          Popular
                        </Badge>
                      )}
                    </div>
                    <h3 className="text-lg font-semibold text-white mb-2">{template.name}</h3>
                    <p className="text-sm text-white/60 mb-3">{template.description}</p>
                    <div className="flex items-center justify-between">
                      <Badge className={getTypeColor(template.type)}>
                        {template.type}
                      </Badge>
                      <Button 
                        size="sm"
                        className="gradient-bg"
                        onClick={() => handleGenerateReport(template.id)}
                      >
                        Generate
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Search and Filters */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Generated Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-5 w-5" />
                <Input
                  placeholder="Search reports..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="ai-chat-input pl-12"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="ai-chat-input px-3 py-2 rounded-lg"
                >
                  <option value="ALL">All Types</option>
                  <option value="FINANCIAL">Financial</option>
                  <option value="SALES">Sales</option>
                  <option value="CUSTOMER">Customer</option>
                  <option value="PROJECT">Project</option>
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="ai-chat-input px-3 py-2 rounded-lg"
                >
                  <option value="ALL">All Status</option>
                  <option value="GENERATED">Generated</option>
                  <option value="GENERATING">Generating</option>
                  <option value="SCHEDULED">Scheduled</option>
                  <option value="FAILED">Failed</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reports List */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">
              Reports ({filteredReports.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredReports.length === 0 ? (
              <div className="text-center py-12">
                <FileBarChart className="h-12 w-12 text-white/20 mx-auto mb-4" />
                <p className="text-white/60 mb-4">
                  {searchTerm || typeFilter !== 'ALL' || statusFilter !== 'ALL' 
                    ? 'No reports found matching your criteria.' 
                    : 'No reports generated yet.'
                  }
                </p>
                {!searchTerm && typeFilter === 'ALL' && statusFilter === 'ALL' && (
                  <Button 
                    className="gradient-bg"
                    onClick={() => {
                      // New report logic
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Generate Your First Report
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredReports.map((report) => (
                  <div key={report.id} className="p-4 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-200">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className={`p-3 rounded-lg ${getTypeColor(report.type)}`}>
                          <FileBarChart className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="text-lg font-semibold text-white">{report.name}</h3>
                            <Badge className={getTypeColor(report.type)}>
                              {report.type}
                            </Badge>
                            <Badge className={getStatusColor(report.status)}>
                              {report.status}
                            </Badge>
                          </div>
                          <p className="text-white/60 mb-2">{report.description}</p>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <p className="text-white/60">Format</p>
                              <p className="text-white font-medium">{report.format}</p>
                            </div>
                            <div>
                              <p className="text-white/60">Created</p>
                              <p className="text-white font-medium">
                                {new Date(report.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                            {report.size && (
                              <div>
                                <p className="text-white/60">Size</p>
                                <p className="text-white font-medium">{report.size}</p>
                              </div>
                            )}
                          </div>
                          
                          {report.generatedAt && (
                            <div className="flex items-center gap-2 mt-2">
                              <CheckCircle className="h-4 w-4 text-green-400" />
                              <span className="text-sm text-green-400">
                                Generated on {new Date(report.generatedAt).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                          
                          {report.scheduledFor && (
                            <div className="flex items-center gap-2 mt-2">
                              <Clock className="h-4 w-4 text-yellow-400" />
                              <span className="text-sm text-yellow-400">
                                Scheduled for {new Date(report.scheduledFor).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {report.status === 'GENERATED' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-white/15"
                            onClick={() => handleDownloadReport(report.id)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-white/15"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-white/15"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-400 hover:bg-red-500/20"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}