'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Users, 
  CreditCard, 
  TrendingUp, 
  Plus, 
  Building, 
  User,
  Activity,
  Sparkles,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'
import AISuggestions from '@/components/ai/ai-suggestions'

interface DashboardStats {
  totalQuotes: number
  totalCustomers: number
  monthlyRevenue: number
  aiUsage: number
  quoteGrowth: number
  customerGrowth: number
  revenueGrowth: number
  aiGrowth: number
}

interface RecentActivity {
  id: string
  type: 'QUOTE' | 'CUSTOMER' | 'PAYMENT' | 'AI'
  title: string
  description: string
  timestamp: string
  user: string
}

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats>({
    totalQuotes: 0,
    totalCustomers: 0,
    monthlyRevenue: 0,
    aiUsage: 0,
    quoteGrowth: 0,
    customerGrowth: 0,
    revenueGrowth: 0,
    aiGrowth: 0
  })
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    // Load dashboard data
    loadDashboardData()
  }, [session, status, router])

  const loadDashboardData = async () => {
    try {
      // Load stats
      const statsResponse = await fetch('/api/dashboard/stats')
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      }

      // Load recent activities
      const activitiesResponse = await fetch('/api/dashboard/activities')
      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json()
        setRecentActivities(activitiesData)
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? ArrowUpRight : ArrowDownRight
  }

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? 'text-green-400' : 'text-red-400'
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'QUOTE': return FileText
      case 'CUSTOMER': return Users
      case 'PAYMENT': return CreditCard
      case 'AI': return Sparkles
      default: return Activity
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'QUOTE': return 'bg-blue-500/20 text-blue-400'
      case 'CUSTOMER': return 'bg-green-500/20 text-green-400'
      case 'PAYMENT': return 'bg-purple-500/20 text-purple-400'
      case 'AI': return 'bg-orange-500/20 text-orange-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'quote':
        router.push('/quotes/new')
        break
      case 'property':
        router.push('/properties/new')
        break
      case 'customer':
        router.push('/customers/new')
        break
      case 'ai':
        router.push('/ai-tools')
        break
      default:
        break
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-background dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
            <Sparkles className="h-8 w-8 text-white" />
          </div>
          <p className="text-white/60">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background dark p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">
              Welcome back, <span className="gradient-text">{session?.user?.name}</span>
            </h1>
            <p className="text-white/60 mt-1">
              {session?.user?.organizationName} Dashboard
            </p>
          </div>
          <Badge variant="secondary" className="gradient-bg text-white">
            {session?.user?.role}
          </Badge>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Quotes Card */}
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Quotes
              </CardTitle>
              <FileText className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.totalQuotes}</div>
              <div className="flex items-center space-x-1 text-xs">
                {getGrowthIcon(stats.quoteGrowth)({ className: `h-3 w-3 ${getGrowthColor(stats.quoteGrowth)}` })}
                <span className={getGrowthColor(stats.quoteGrowth)}>
                  {stats.quoteGrowth > 0 ? '+' : ''}{stats.quoteGrowth}% from last month
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Customers Card */}
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Active Customers
              </CardTitle>
              <Users className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.totalCustomers}</div>
              <div className="flex items-center space-x-1 text-xs">
                {getGrowthIcon(stats.customerGrowth)({ className: `h-3 w-3 ${getGrowthColor(stats.customerGrowth)}` })}
                <span className={getGrowthColor(stats.customerGrowth)}>
                  {stats.customerGrowth > 0 ? '+' : ''}{stats.customerGrowth}% from last month
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Revenue Card */}
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Monthly Revenue
              </CardTitle>
              <CreditCard className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                €{stats.monthlyRevenue.toLocaleString()}
              </div>
              <div className="flex items-center space-x-1 text-xs">
                {getGrowthIcon(stats.revenueGrowth)({ className: `h-3 w-3 ${getGrowthColor(stats.revenueGrowth)}` })}
                <span className={getGrowthColor(stats.revenueGrowth)}>
                  {stats.revenueGrowth > 0 ? '+' : ''}{stats.revenueGrowth}% from last month
                </span>
              </div>
            </CardContent>
          </Card>

          {/* AI Usage Card */}
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                AI Usage
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-orange-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.aiUsage}%</div>
              <div className="flex items-center space-x-1 text-xs">
                {getGrowthIcon(stats.aiGrowth)({ className: `h-3 w-3 ${getGrowthColor(stats.aiGrowth)}` })}
                <span className={getGrowthColor(stats.aiGrowth)}>
                  {stats.aiGrowth > 0 ? '+' : ''}{stats.aiGrowth}% from last month
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button 
                className="quick-action-btn h-32"
                onClick={() => handleQuickAction('quote')}
              >
                <FileText className="h-8 w-8 text-blue-400 mb-2" />
                <span className="text-white font-medium">New Quote</span>
                <span className="text-white/60 text-sm">Create professional quotes</span>
              </Button>
              
              <Button 
                className="quick-action-btn h-32"
                onClick={() => handleQuickAction('property')}
              >
                <Building className="h-8 w-8 text-green-400 mb-2" />
                <span className="text-white font-medium">Add Property</span>
                <span className="text-white/60 text-sm">Manage properties</span>
              </Button>
              
              <Button 
                className="quick-action-btn h-32"
                onClick={() => handleQuickAction('customer')}
              >
                <User className="h-8 w-8 text-purple-400 mb-2" />
                <span className="text-white font-medium">Add Customer</span>
                <span className="text-white/60 text-sm">Manage customers</span>
              </Button>
              
              <Button 
                className="quick-action-btn h-32"
                onClick={() => handleQuickAction('ai')}
              >
                <Sparkles className="h-8 w-8 text-orange-400 mb-2" />
                <span className="text-white font-medium">AI Assistant</span>
                <span className="text-white/60 text-sm">Get AI help</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Recent Activities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.length === 0 ? (
                <p className="text-white/60 text-center py-8">No recent activities</p>
              ) : (
                recentActivities.map((activity) => {
                  const Icon = getActivityIcon(activity.type)
                  return (
                    <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg bg-white/5">
                      <div className={`p-2 rounded-lg ${getActivityColor(activity.type)}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="text-white font-medium">{activity.title}</h4>
                          <span className="text-white/40 text-sm">{activity.timestamp}</span>
                        </div>
                        <p className="text-white/60 text-sm">{activity.description}</p>
                        <p className="text-white/40 text-xs mt-1">by {activity.user}</p>
                      </div>
                    </div>
                  )
                })
              )}
            </div>
          </CardContent>
        </Card>

        {/* AI Suggestions */}
        {session?.user?.organizationId && (
          <div className="space-y-4">
            <h2 className="text-xl font-bold text-white">AI Insights</h2>
            <AISuggestions organizationId={session.user.organizationId} />
          </div>
        )}

        {/* Project Rules Card */}
        <div className="glass-card bg-white/10 backdrop-blur-md border border-white/10 shadow-lg rounded-2xl p-6 mt-8">
          <h2 className="text-xl font-bold text-white mb-4">Projectregels</h2>
          <ul className="list-disc pl-6 text-white/80 space-y-2">
            <li>Alle offertes dienen binnen 24 uur na aanvraag te worden aangemaakt.</li>
            <li>Projecten moeten minimaal één keer per week worden bijgewerkt.</li>
            <li>Klantgegevens dienen up-to-date te zijn voor facturatie.</li>
            <li>Gebruik AI-suggesties voor het optimaliseren van offertes.</li>
            <li>Volg altijd de AVG-richtlijnen bij klantcommunicatie.</li>
          </ul>
        </div>
      </div>
    </div>
  )
}