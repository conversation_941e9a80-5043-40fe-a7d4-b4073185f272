@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  --background: #ffffff;
  --foreground: #1a1d3a;
  --card: #ffffff;
  --card-foreground: #1a1d3a;
  --popover: #ffffff;
  --popover-foreground: #1a1d3a;
  --primary: #1a1d3a;
  --primary-foreground: #ffffff;
  --secondary: #f8fafc;
  --secondary-foreground: #1a1d3a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #1a1d3a;
  --destructive: #ef4444;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #1a1d3a;
  --chart-1: #22c55e;
  --chart-2: #3b82f6;
  --chart-3: #8b5cf6;
  --chart-4: #f59e0b;
  --chart-5: #ef4444;
  --sidebar: #ffffff;
  --sidebar-foreground: #1a1d3a;
  --sidebar-primary: #1a1d3a;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #1a1d3a;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #1a1d3a;
}

.dark {
  --background: #0f1629;
  --foreground: #ffffff;
  --card: rgba(26, 29, 58, 0.8);
  --card-foreground: #ffffff;
  --popover: rgba(26, 29, 58, 0.9);
  --popover-foreground: #ffffff;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: rgba(255, 255, 255, 0.15);
  --secondary-foreground: #ffffff;
  --muted: rgba(255, 255, 255, 0.08);
  --muted-foreground: #a1a1aa;
  --accent: rgba(59, 130, 246, 0.2);
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --border: rgba(255, 255, 255, 0.15);
  --input: rgba(255, 255, 255, 0.12);
  --ring: #3b82f6;
  --chart-1: #22c55e;
  --chart-2: #3b82f6;
  --chart-3: #8b5cf6;
  --chart-4: #f59e0b;
  --chart-5: #ef4444;
  --sidebar: rgba(26, 29, 58, 0.95);
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: rgba(255, 255, 255, 0.1);
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: rgba(255, 255, 255, 0.15);
  --sidebar-ring: #3b82f6;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, #0f1629 0%, #1a1d3a 50%, #2a2d5a 100%);
    min-height: 100vh;
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer utilities {
  .glass-card {
    backdrop-filter: blur(20px);
    background: rgba(26, 29, 58, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .glass-card-hover {
    backdrop-filter: blur(20px);
    background: rgba(26, 29, 58, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }
  
  .glass-card-hover:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 
      0 12px 48px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: scale(1.02);
  }
  
  .gradient-text {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #22c55e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.3));
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  }
  
  .sidebar-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    border-radius: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.2s ease;
  }
  
  .sidebar-item:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    padding-left: 1.25rem;
  }
  
  .sidebar-item-active {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    border-radius: 0.75rem;
    color: white;
    background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(139, 92, 246, 0.2));
    border-left: 4px solid #60a5fa;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
  }
  
  .stat-card {
    backdrop-filter: blur(20px);
    background: rgba(26, 29, 58, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }
  
  .stat-card:hover {
    box-shadow: 
      0 16px 64px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
  
  .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
  }
  
  .stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(180deg, transparent, rgba(59, 130, 246, 0.5), transparent);
  }
  
  .quick-action-btn {
    backdrop-filter: blur(20px);
    background: rgba(26, 29, 58, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .quick-action-btn:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 
      0 16px 64px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: scale(1.05);
  }
  
  .ai-chat-input {
    backdrop-filter: blur(20px);
    background: rgba(26, 29, 58, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 12px;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    width: 100%;
    color: white;
    background-color: rgba(255, 255, 255, 0.08);
  }
  
  .ai-chat-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  .ai-chat-input:focus {
    outline: none;
    ring: 2px solid #60a5fa;
    border-color: transparent;
  }
  
  .mobile-menu-btn {
    backdrop-filter: blur(20px);
    background: rgba(26, 29, 58, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 12px;
    padding: 0.75rem;
    display: none;
    color: white;
    transition: all 0.2s ease;
  }
  
  .mobile-menu-btn:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: scale(1.1);
  }
  
  @media (max-width: 1024px) {
    .mobile-menu-btn {
      display: flex;
    }
  }
  
  .glow-effect {
    box-shadow: 
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(139, 92, 246, 0.2),
      0 0 60px rgba(34, 197, 94, 0.1);
  }
  
  .text-glow {
    text-shadow: 
      0 0 10px rgba(59, 130, 246, 0.5),
      0 0 20px rgba(139, 92, 246, 0.3),
      0 0 30px rgba(34, 197, 94, 0.2);
  }
  
  .pulse-animation {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
  
  .slide-in {
    animation: slideIn 0.5s ease-out;
  }
  
  @keyframes slideIn {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}
