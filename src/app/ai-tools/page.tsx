'use client'

import { useState, useRef, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Sparkles, 
  Send, 
  Bot, 
  User,
  FileText,
  Home,
  Bath,
  Zap,
  Building,
  Loader2,
  Volume2,
  VolumeX,
  Pause,
  Play
} from 'lucide-react'
import { VoiceInput } from '@/components/ai/voice-input'
import { SmartSuggestions } from '@/components/ai/smart-suggestions'
import { QuoteGenerator } from '@/components/ai/quote-generator'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  type?: 'text' | 'suggestion' | 'quote'
  metadata?: any
}

interface QuickAction {
  id: string
  title: string
  description: string
  icon: any
  color: string
  prompt: string
}

const quickActions: QuickAction[] = [
  {
    id: 'bathroom',
    title: 'Badkamer Renovatie',
    description: 'Complete badkamer renovatie offerte',
    icon: Bath,
    color: 'from-blue-500 to-cyan-500',
    prompt: 'Maak een gedetailleerde offerte voor een complete badkamerrenovatie inclusief materialen, arbeid en btw'
  },
  {
    id: 'roof',
    title: 'Dakisolatie',
    description: 'Dakisolatie met energiebesparing',
    icon: Home,
    color: 'from-green-500 to-emerald-500',
    prompt: 'Genereer een offerte voor dakisolatie met berekening van energiebesparing en terugverdientijd'
  },
  {
    id: 'solar',
    title: 'Zonnepanelen',
    description: 'Zonnepanelen installatie',
    icon: Zap,
    color: 'from-yellow-500 to-orange-500',
    prompt: 'Creëer een offerte voor zonnepanelen installatie met specificaties, opbrengst en terugverdientijd'
  },
  {
    id: 'extension',
    title: 'Uitbouw',
    description: 'Woning uitbouw project',
    icon: Building,
    color: 'from-purple-500 to-pink-500',
    prompt: 'Maak een offerte voor een woning uitbouw met bouwtekeningen, materialen en planning'
  }
]

const suggestions = []

export default function AIToolsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Hallo! Ik ben je AI assistent. Ik kan je helpen met het maken van offertes, calculaties en projectadvies. Waar kan ik je mee helpen?',
      timestamp: new Date(),
      type: 'text'
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (status === 'loading') return
    if (!session) {
      router.push('/auth/login')
    }
  }, [session, status, router])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (content: string, type: 'text' | 'suggestion' = 'text') => {
    if (!content.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: new Date(),
      type
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsProcessing(true)

    try {
      // Get conversation history for context
      const conversationHistory = messages.slice(-10) // Keep last 10 messages for context
      
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          conversationHistory,
          type
        })
      })

      if (!response.ok) {
        throw new Error('Failed to get AI response')
      }

      const data = await response.json()
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.response,
        timestamp: new Date(data.timestamp),
        type: data.type,
        metadata: data.metadata
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error processing message:', error)
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Excuses, er is iets misgegaan. Probeer het alsjeblieft opnieuw.',
        timestamp: new Date(),
        type: 'text'
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsProcessing(false)
    }
  }

  const handleQuickAction = (action: QuickAction) => {
    handleSendMessage(action.prompt, 'suggestion')
  }

  const handleVoiceTranscript = (transcript: string) => {
    setInputValue(transcript)
    // Auto-send the transcript if desired
    // handleSendMessage(transcript)
  }

  const handleSuggestionSelect = (suggestion: any) => {
    if (suggestion.action.type === 'message') {
      handleSendMessage(suggestion.action.data.message)
    } else if (suggestion.action.type === 'template') {
      handleSendMessage(`Maak een offerte op basis van template: ${suggestion.action.data.templateId}`)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(inputValue)
    }
  }

  const toggleSpeech = (content: string) => {
    if (isSpeaking) {
      window.speechSynthesis.cancel()
      setIsSpeaking(false)
      return
    }

    const utterance = new SpeechSynthesisUtterance(content)
    utterance.lang = 'nl-NL'
    utterance.onend = () => setIsSpeaking(false)
    window.speechSynthesis.speak(utterance)
    setIsSpeaking(true)
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
            <Sparkles className="h-8 w-8 text-white" />
          </div>
          <p className="text-white/60">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center lg:text-left slide-in">
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4 text-glow">
            AI <span className="gradient-text">Assistant</span>
          </h1>
          <p className="text-lg text-white/80 max-w-2xl">
            Your intelligent companion for quote generation, project planning, and business insights.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Tabs defaultValue="chat" className="space-y-6">
              <TabsList className="grid w-full grid-cols-2 glass-card">
                <TabsTrigger value="chat" className="text-white data-[state=active]:bg-white/20">
                  <Bot className="h-4 w-4 mr-2" />
                  AI Chat
                </TabsTrigger>
                <TabsTrigger value="quote" className="text-white data-[state=active]:bg-white/20">
                  <FileText className="h-4 w-4 mr-2" />
                  Quote Generator
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="chat" className="space-y-6">
                {/* Main Chat Interface */}
                <Card className="stat-card h-[600px] flex flex-col">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-white flex items-center gap-3 text-xl">
                      <Bot className="h-6 w-6 text-purple-400" />
                      AI Conversation
                      <Badge variant="outline" className="ml-auto text-purple-300 border-purple-400/30">
                        Online
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 flex flex-col">
                    {/* Messages */}
                    <ScrollArea className="flex-1 pr-4 mb-4">
                      <div className="space-y-4">
                        {messages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex gap-3 ${
                              message.role === 'user' ? 'justify-end' : 'justify-start'
                            }`}
                          >
                            {message.role === 'assistant' && (
                              <div className="w-8 h-8 gradient-bg rounded-full flex items-center justify-center flex-shrink-0">
                                <Bot className="h-4 w-4 text-white" />
                              </div>
                            )}
                            <div
                              className={`max-w-[80%] rounded-2xl p-4 ${
                                message.role === 'user'
                                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                                  : 'glass-card border border-white/20'
                              }`}
                            >
                              <div className="text-sm whitespace-pre-line">{message.content}</div>
                              <div className="flex items-center gap-2 mt-2">
                                <span className="text-xs opacity-60">
                                  {message.timestamp.toLocaleTimeString()}
                                </span>
                                {message.role === 'assistant' && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleSpeech(message.content)}
                                    className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                                  >
                                    {isSpeaking ? (
                                      <VolumeX className="h-3 w-3" />
                                    ) : (
                                      <Volume2 className="h-3 w-3" />
                                    )}
                                  </Button>
                                )}
                              </div>
                            </div>
                            {message.role === 'user' && (
                              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <User className="h-4 w-4 text-white" />
                              </div>
                            )}
                          </div>
                        ))}
                        {isProcessing && (
                          <div className="flex gap-3">
                            <div className="w-8 h-8 gradient-bg rounded-full flex items-center justify-center flex-shrink-0">
                              <Bot className="h-4 w-4 text-white" />
                            </div>
                            <div className="glass-card border border-white/20 rounded-2xl p-4">
                              <div className="flex items-center gap-2">
                                <Loader2 className="h-4 w-4 animate-spin text-purple-400" />
                                <span className="text-sm text-white/60">AI is thinking...</span>
                              </div>
                            </div>
                          </div>
                        )}
                        <div ref={messagesEndRef} />
                      </div>
                    </ScrollArea>

                    {/* Input Area */}
                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <Textarea
                          ref={textareaRef}
                          value={inputValue}
                          onChange={(e) => setInputValue(e.target.value)}
                          onKeyPress={handleKeyPress}
                          placeholder="Type your message or use voice input..."
                          className="ai-chat-input resize-none"
                          rows={2}
                        />
                        <VoiceInput
                          onTranscript={handleVoiceTranscript}
                          disabled={isProcessing}
                          className="self-end"
                        />
                        <Button
                          onClick={() => handleSendMessage(inputValue)}
                          disabled={!inputValue.trim() || isProcessing}
                          className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105 self-end"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="quote" className="space-y-6">
                <QuoteGenerator />
              </TabsContent>
            </Tabs>
          </div>

          {/* Quick Actions & Suggestions */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="stat-card">
              <CardHeader className="pb-4">
                <CardTitle className="text-white flex items-center gap-3 text-lg">
                  <Sparkles className="h-5 w-5 text-purple-400" />
                  Snelle Acties
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {quickActions.map((action) => {
                    const Icon = action.icon
                    return (
                      <button
                        key={action.id}
                        onClick={() => handleQuickAction(action)}
                        className="w-full text-left p-3 rounded-xl glass-card-hover hover:bg-white/10 transition-all duration-200 group"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 bg-gradient-to-r ${action.color} rounded-xl flex items-center justify-center glow-effect group-hover:scale-110 transition-transform duration-200`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="text-white font-medium text-sm group-hover:text-blue-300 transition-colors">
                              {action.title}
                            </div>
                            <div className="text-white/60 text-xs group-hover:text-white/80 transition-colors">
                              {action.description}
                            </div>
                          </div>
                        </div>
                      </button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Smart Suggestions */}
            <SmartSuggestions
              onSuggestionSelect={handleSuggestionSelect}
              context={{
                projectType: 'general',
                urgency: 'normal'
              }}
              conversationHistory={messages.map(msg => ({
                role: msg.role,
                content: msg.content
              }))}
            />
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}