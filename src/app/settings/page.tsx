'use client'

import { useSession } from 'next-auth/react'
import { useRout<PERSON> } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  Settings, 
  User, 
  Building, 
  Mail, 
  Phone, 
  Bell,
  Shield,
  Database,
  Palette,
  CreditCard,
  Save,
  RefreshCw,
  Download,
  Upload,
  Trash2,
  Plus,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { DashboardLayout } from '@/components/dashboard/layout'
import { EmailConfig } from '@/components/email/email-config'
import { EmailTemplates } from '@/components/email/email-templates'
import { WhatsAppConfig } from '@/components/whatsapp/whatsapp-config'

interface UserSettings {
  name: string
  email: string
  phone: string
  avatar: string
  role: string
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
}

interface OrganizationSettings {
  name: string
  domain: string
  address: {
    street: string
    city: string
    postalCode: string
    country: string
  }
  phone: string
  email: string
  website: string
  vatNumber: string
  logo: string
  branding: {
    primaryColor: string
    secondaryColor: string
    logo: string
  }
}

interface IntegrationSettings {
  email: {
    provider: 'gmail' | 'outlook' | 'smtp' | 'sendgrid' | 'resend'
    smtpHost?: string
    smtpPort?: number
    smtpUsername?: string
    smtpPassword?: string
    fromAddress: string
    fromName: string
  }
  whatsapp: {
    token: string
    phoneNumber: string
    businessId: string
  }
  stripe: {
    publicKey: string
    secretKey: string
    webhookSecret: string
  }
}

export default function SettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<'profile' | 'organization' | 'integrations' | 'billing' | 'security'>('profile')
  const [integrationSubTab, setIntegrationSubTab] = useState<'email' | 'whatsapp' | 'templates'>('email')
  const [userSettings, setUserSettings] = useState<UserSettings>({
    name: '',
    email: '',
    phone: '',
    avatar: '',
    role: '',
    notifications: {
      email: true,
      push: true,
      sms: false
    }
  })
  const [organizationSettings, setOrganizationSettings] = useState<OrganizationSettings>({
    name: '',
    domain: '',
    address: {
      street: '',
      city: '',
      postalCode: '',
      country: ''
    },
    phone: '',
    email: '',
    website: '',
    vatNumber: '',
    logo: '',
    branding: {
      primaryColor: '#3b82f6',
      secondaryColor: '#8b5cf6',
      logo: ''
    }
  })
  const [integrationSettings, setIntegrationSettings] = useState<IntegrationSettings>({
    email: {
      provider: 'gmail',
      fromAddress: '',
      fromName: ''
    },
    whatsapp: {
      token: '',
      phoneNumber: '',
      businessId: ''
    },
    stripe: {
      publicKey: '',
      secretKey: '',
      webhookSecret: ''
    }
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    loadSettings()
  }, [session, status, router])

  const loadSettings = async () => {
    try {
      // Mock settings data
      setUserSettings({
        name: session?.user?.name || 'John Doe',
        email: session?.user?.email || '<EMAIL>',
        phone: '+31 6 12345678',
        avatar: '',
        role: session?.user?.role || 'Admin',
        notifications: {
          email: true,
          push: true,
          sms: false
        }
      })

      setOrganizationSettings({
        name: session?.user?.organizationName || 'WindowPro BV',
        domain: 'windowpro.be',
        address: {
          street: 'Bedrijfsstraat 123',
          city: 'Amsterdam',
          postalCode: '1000 AA',
          country: 'Netherlands'
        },
        phone: '+31 20 1234567',
        email: '<EMAIL>',
        website: 'https://windowpro.be',
        vatNumber: 'NL123456789B01',
        logo: '',
        branding: {
          primaryColor: '#3b82f6',
          secondaryColor: '#8b5cf6',
          logo: ''
        }
      })

      setIntegrationSettings({
        email: {
          provider: 'gmail',
          fromAddress: '<EMAIL>',
          fromName: 'WindowPro BV'
        },
        whatsapp: {
          token: 'whatsapp_token_here',
          phoneNumber: '+***********',
          businessId: 'business_id_here'
        },
        stripe: {
          publicKey: 'pk_test_...',
          secretKey: 'sk_test_...',
          webhookSecret: 'whsec_...'
        }
      })
    } catch (error) {
      console.error('Error loading settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveSettings = async () => {
    setIsSaving(true)
    try {
      // Save settings logic here
      console.log('Saving settings...')
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
    } catch (error) {
      console.error('Error saving settings:', error)
    } finally {
      setIsSaving(false)
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
              <Settings className="h-8 w-8 text-white" />
            </div>
            <p className="text-white/60">Loading settings...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">Settings</h1>
            <p className="text-white/60 mt-1">
              Manage your account, organization, and integrations
            </p>
          </div>
          <Button 
            className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
            onClick={handleSaveSettings}
            disabled={isSaving}
          >
            {isSaving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        {/* Navigation Tabs */}
        <Card className="glass-card">
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'profile', label: 'Profile', icon: User },
                { id: 'organization', label: 'Organization', icon: Building },
                { id: 'integrations', label: 'Integrations', icon: Database },
                { id: 'billing', label: 'Billing', icon: CreditCard },
                { id: 'security', label: 'Security', icon: Shield }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <Button
                    key={tab.id}
                    variant={activeTab === tab.id ? 'default' : 'ghost'}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={activeTab === tab.id ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {tab.label}
                  </Button>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Tab Content */}
        {activeTab === 'profile' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white">Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Full Name</label>
                  <Input
                    value={userSettings.name}
                    onChange={(e) => setUserSettings(prev => ({ ...prev, name: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Email Address</label>
                  <Input
                    type="email"
                    value={userSettings.email}
                    onChange={(e) => setUserSettings(prev => ({ ...prev, email: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Phone Number</label>
                  <Input
                    type="tel"
                    value={userSettings.phone}
                    onChange={(e) => setUserSettings(prev => ({ ...prev, phone: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Role</label>
                  <Input
                    value={userSettings.role}
                    disabled
                    className="ai-chat-input bg-white/5"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white">Notification Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white font-medium">Email Notifications</p>
                    <p className="text-sm text-white/60">Receive updates via email</p>
                  </div>
                  <Button
                    variant={userSettings.notifications.email ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setUserSettings(prev => ({
                      ...prev,
                      notifications: { ...prev.notifications, email: !prev.notifications.email }
                    }))}
                    className={userSettings.notifications.email ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                  >
                    {userSettings.notifications.email ? 'Enabled' : 'Disabled'}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white font-medium">Push Notifications</p>
                    <p className="text-sm text-white/60">Receive push notifications</p>
                  </div>
                  <Button
                    variant={userSettings.notifications.push ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setUserSettings(prev => ({
                      ...prev,
                      notifications: { ...prev.notifications, push: !prev.notifications.push }
                    }))}
                    className={userSettings.notifications.push ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                  >
                    {userSettings.notifications.push ? 'Enabled' : 'Disabled'}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white font-medium">SMS Notifications</p>
                    <p className="text-sm text-white/60">Receive updates via SMS</p>
                  </div>
                  <Button
                    variant={userSettings.notifications.sms ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setUserSettings(prev => ({
                      ...prev,
                      notifications: { ...prev.notifications, sms: !prev.notifications.sms }
                    }))}
                    className={userSettings.notifications.sms ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                  >
                    {userSettings.notifications.sms ? 'Enabled' : 'Disabled'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'organization' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white">Organization Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Organization Name</label>
                  <Input
                    value={organizationSettings.name}
                    onChange={(e) => setOrganizationSettings(prev => ({ ...prev, name: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Domain</label>
                  <Input
                    value={organizationSettings.domain}
                    onChange={(e) => setOrganizationSettings(prev => ({ ...prev, domain: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Phone</label>
                  <Input
                    type="tel"
                    value={organizationSettings.phone}
                    onChange={(e) => setOrganizationSettings(prev => ({ ...prev, phone: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Email</label>
                  <Input
                    type="email"
                    value={organizationSettings.email}
                    onChange={(e) => setOrganizationSettings(prev => ({ ...prev, email: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Website</label>
                  <Input
                    type="url"
                    value={organizationSettings.website}
                    onChange={(e) => setOrganizationSettings(prev => ({ ...prev, website: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">VAT Number</label>
                  <Input
                    value={organizationSettings.vatNumber}
                    onChange={(e) => setOrganizationSettings(prev => ({ ...prev, vatNumber: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white">Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Street</label>
                  <Input
                    value={organizationSettings.address.street}
                    onChange={(e) => setOrganizationSettings(prev => ({
                      ...prev,
                      address: { ...prev.address, street: e.target.value }
                    }))}
                    className="ai-chat-input"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-white/60 mb-2 block">City</label>
                    <Input
                      value={organizationSettings.address.city}
                      onChange={(e) => setOrganizationSettings(prev => ({
                        ...prev,
                        address: { ...prev.address, city: e.target.value }
                      }))}
                      className="ai-chat-input"
                    />
                  </div>
                  <div>
                    <label className="text-sm text-white/60 mb-2 block">Postal Code</label>
                    <Input
                      value={organizationSettings.address.postalCode}
                      onChange={(e) => setOrganizationSettings(prev => ({
                        ...prev,
                        address: { ...prev.address, postalCode: e.target.value }
                      }))}
                      className="ai-chat-input"
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm text-white/60 mb-2 block">Country</label>
                  <Input
                    value={organizationSettings.address.country}
                    onChange={(e) => setOrganizationSettings(prev => ({
                      ...prev,
                      address: { ...prev.address, country: e.target.value }
                    }))}
                    className="ai-chat-input"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'integrations' && (
          <div className="space-y-6">
            {/* Integration Sub-Tabs */}
            <Card className="glass-card">
              <CardContent className="pt-6">
                <div className="flex flex-wrap gap-2">
                  {[
                    { id: 'email', label: 'Email Config', icon: Mail },
                    { id: 'templates', label: 'Email Templates', icon: Mail },
                    { id: 'whatsapp', label: 'WhatsApp', icon: Phone }
                  ].map((tab) => {
                    const Icon = tab.icon
                    return (
                      <Button
                        key={tab.id}
                        variant={integrationSubTab === tab.id ? 'default' : 'ghost'}
                        onClick={() => setIntegrationSubTab(tab.id as any)}
                        className={integrationSubTab === tab.id ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                      >
                        <Icon className="h-4 w-4 mr-2" />
                        {tab.label}
                      </Button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Integration Sub-Tab Content */}
            {integrationSubTab === 'email' && (
              <EmailConfig 
                onConfigSave={(config) => {
                  console.log('Email config saved:', config)
                }}
              />
            )}

            {integrationSubTab === 'templates' && (
              <EmailTemplates 
                onTemplateSelect={(template) => {
                  console.log('Template selected:', template)
                }}
              />
            )}

            {integrationSubTab === 'whatsapp' && (
              <WhatsAppConfig 
                onConfigSave={(config) => {
                  console.log('WhatsApp config saved:', config)
                }}
              />
            )}
          </div>
        )}

        {activeTab === 'billing' && (
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-white">Billing Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <CreditCard className="h-12 w-12 text-white/20 mx-auto mb-4" />
                <p className="text-white/60 mb-4">Billing management coming soon</p>
                <p className="text-sm text-white/40">
                  Manage your subscription, payment methods, and billing history
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'security' && (
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-white">Security Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Shield className="h-12 w-12 text-white/20 mx-auto mb-4" />
                <p className="text-white/60 mb-4">Security settings coming soon</p>
                <p className="text-sm text-white/40">
                  Manage your password, two-factor authentication, and security preferences
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}