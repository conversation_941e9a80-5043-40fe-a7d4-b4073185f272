import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { SessionProvider } from "@/components/providers/session-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Quote.AI+CRM - AI-Powered Quote Generation",
  description: "Transform your business with AI-powered quote generation and comprehensive customer relationship management.",
  keywords: ["Quote.AI", "CRM", "AI", "Business", "Quote Generation", "Customer Management"],
  authors: [{ name: "Quote.AI Team" }],
  openGraph: {
    title: "Quote.AI+CRM",
    description: "AI-powered quote generation and CRM platform",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Quote.AI+CRM",
    description: "AI-powered quote generation and CRM platform",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}
      >
        <SessionProvider>
          {children}
          <Toaster />
        </SessionProvider>
      </body>
    </html>
  );
}
