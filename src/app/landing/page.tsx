'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Sparkles, 
  FileText, 
  Users, 
  Building, 
  Bot, 
  ArrowRight, 
  CheckCircle,
  Star,
  TrendingUp,
  Shield,
  Zap,
  MessageSquare,
  CreditCard,
  BarChart3,
  Play,
  Mail,
  Phone,
  MapPin
} from 'lucide-react'

const features = [
  {
    icon: FileText,
    title: 'AI Offerte Generatie',
    description: 'Genereer professionele offertes in seconden met onze geavanceerde AI technologie',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: Bo<PERSON>,
    title: 'Intelligente Assistant',
    description: '24/7 AI assistent voor projectadvies, prijscalculaties en materiaalkeuzes',
    color: 'from-purple-500 to-pink-500'
  },
  {
    icon: Users,
    title: 'CRM Integratie',
    description: 'Complete klantrelatiebeheer met contacten, projecten en communicatie',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: Building,
    title: 'Project Management',
    description: '<PERSON>heer al je bouwprojecten, planningen en documenten op één plek',
    color: 'from-orange-500 to-red-500'
  },
  {
    icon: MessageSquare,
    title: 'WhatsApp & Email',
    description: 'Integratie met WhatsApp Business en email voor klantcommunicatie',
    color: 'from-yellow-500 to-orange-500'
  },
  {
    icon: CreditCard,
    title: 'Betalingen & Facturatie',
    description: 'Automatische facturatie, betalingsverwerking en financiële rapportage',
    color: 'from-indigo-500 to-purple-500'
  }
]

const pricingPlans = [
  {
    name: 'FREE',
    price: '€0',
    period: 'per maand',
    description: 'Perfect voor starters en kleine projecten',
    features: [
      '1 gebruiker',
      '10 offertes per maand',
      'Basis AI features (20 requests)',
      'Email support',
      'Mobiele app'
    ],
    cta: 'Start Gratis',
    popular: false,
    color: 'from-gray-500 to-gray-600'
  },
  {
    name: 'STARTER',
    price: '€29',
    period: 'per maand',
    description: 'Ideal voor groeiende bedrijven',
    features: [
      '5 gebruikers',
      '200 offertes per maand',
      'Advanced AI (1000 requests)',
      'WhatsApp integratie',
      'Email templates',
      'Priority support'
    ],
    cta: 'Probeer Starter',
    popular: true,
    color: 'from-blue-500 to-purple-500'
  },
  {
    name: 'PROFESSIONAL',
    price: '€99',
    period: 'per maand',
    description: 'Voor professionele bedrijven',
    features: [
      '25 gebruikers',
      'Unlimited offertes',
      'Premium AI (10,000 requests)',
      'Custom branding',
      'API access',
      'Advanced analytics',
      'Phone support'
    ],
    cta: 'Start Professional',
    popular: false,
    color: 'from-green-500 to-emerald-500'
  },
  {
    name: 'ENTERPRISE',
    price: '€299',
    period: 'per maand',
    description: 'Voor grote organisaties',
    features: [
      'Unlimited gebruikers',
      'Unlimited offertes',
      'Enterprise AI (50,000 requests)',
      'Custom domain',
      'White label',
      'Dedicated support',
      'Custom integrations',
      'SLA guarantee'
    ],
    cta: 'Contact Sales',
    popular: false,
    color: 'from-purple-500 to-pink-500'
  }
]

const testimonials = [
  {
    name: 'Jan de Vries',
    company: 'Vries Bouw',
    role: 'Eigenaar',
    content: 'Quote.AI+CRM heeft onze offerte proces volledig geautomatiseerd. We besparen uren per week en de kwaliteit is stukken beter.',
    rating: 5
  },
  {
    name: 'Maria Jansen',
    company: 'Jansen Renovaties',
    role: 'Projectmanager',
    content: 'De AI assistant is ongelofelijk. Hij geeft perfect advies over materialen en prijzen. Onze klanten zijn zeer tevreden.',
    rating: 5
  },
  {
    name: 'Peter Bakker',
    company: 'Bakker Installaties',
    role: 'Directeur',
    content: 'Beste investering dit jaar. De CRM functies zijn uitstekend en de WhatsApp integratie werkt perfect.',
    rating: 5
  }
]

export default function LandingPage() {
  const router = useRouter()
  const [activeTestimonial, setActiveTestimonial] = useState(0)

  const handleGetStarted = () => {
    router.push('/auth/register')
  }

  const handlePricingClick = (plan: string) => {
    if (plan === 'ENTERPRISE') {
      // Open contact modal or scroll to contact section
      document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })
    } else {
      router.push('/auth/register')
    }
  }

  return (
    <div className="min-h-screen bg-background dark">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20"></div>
        
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 w-80 h-80 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Logo and badge */}
          <div className="mb-8">
            <div className="inline-flex items-center gap-3 glass-card px-6 py-3 rounded-full mb-6">
              <div className="w-10 h-10 gradient-bg rounded-xl flex items-center justify-center glow-effect">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <span className="text-white font-bold text-xl">Quote.AI+CRM</span>
            </div>
            <Badge className="bg-green-500/20 text-green-300 border-green-400/30 mb-6">
              <CheckCircle className="h-4 w-4 mr-2" />
              Nu beschikbaar in Nederland & België
            </Badge>
          </div>

          {/* Main heading */}
          <h1 className="text-5xl lg:text-7xl font-bold text-white mb-6 text-glow">
            Transformeer je
            <span className="gradient-text block mt-2">Bouwbedrijf</span>
          </h1>

          <p className="text-xl lg:text-2xl text-white/80 max-w-4xl mx-auto mb-12 leading-relaxed">
            De kracht van AI gecombineerd met professioneel CRM. Genereer offertes in seconden, 
            beheer klantrelaties en automatiseer je bedrijfsprocessen.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Button 
              onClick={handleGetStarted}
              size="lg"
              className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105 text-lg px-8 py-4"
            >
              Start Gratis Proefperiode
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-white/20 text-white hover:bg-white/10 text-lg px-8 py-4"
              onClick={() => document.getElementById('demo')?.scrollIntoView({ behavior: 'smooth' })}
            >
              <Play className="h-5 w-5 mr-2" />
              Bekijk Demo
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {[
              { label: 'Actieve Gebruikers', value: '2,500+', icon: Users },
              { label: 'Gegenereerde Offertes', value: '50,000+', icon: FileText },
              { label: 'Tijdsbesparing', value: '70%', icon: Zap },
              { label: 'Klanttevredenheid', value: '98%', icon: Star }
            ].map((stat, index) => (
              <div key={index} className="glass-card p-6 rounded-2xl">
                <stat.icon className="h-8 w-8 text-blue-400 mb-3 mx-auto" />
                <div className="text-3xl font-bold text-white mb-1">{stat.value}</div>
                <div className="text-sm text-white/60">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Alles wat je nodig hebt voor je
              <span className="gradient-text block">Bouwbedrijf</span>
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Van offerte generatie tot klantbeheer, alles op één platform ontworpen voor de bouwsector.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card key={index} className="stat-card group cursor-pointer">
                  <CardContent className="p-8">
                    <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mb-6 glow-effect group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4 group-hover:text-blue-300 transition-colors">
                      {feature.title}
                    </h3>
                    <p className="text-white/70 leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-transparent to-white/5">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Kies het plan dat bij je
              <span className="gradient-text block">Bedrijf past</span>
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Schaal op je eigen tempo. Upgrade of downgrade op elk moment.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {pricingPlans.map((plan, index) => (
              <Card key={index} className={`stat-card relative ${plan.popular ? 'ring-2 ring-blue-400/50 scale-105' : ''}`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2">
                      <Star className="h-4 w-4 mr-2" />
                      Meest Populair
                    </Badge>
                  </div>
                )}
                
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                    <div className="mb-4">
                      <span className="text-4xl font-bold text-white">{plan.price}</span>
                      <span className="text-white/60">/{plan.period}</span>
                    </div>
                    <p className="text-white/70 text-sm">{plan.description}</p>
                  </div>

                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
                        <span className="text-white/80 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button 
                    onClick={() => handlePricingClick(plan.name)}
                    className={`w-full ${plan.popular ? 'gradient-bg' : 'bg-white/10 hover:bg-white/20'} text-white transition-all duration-300`}
                    size="lg"
                  >
                    {plan.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Wat onze
              <span className="gradient-text block">Klanten zeggen</span>
            </h2>
          </div>

          <div className="relative">
            <Card className="stat-card p-8">
              <CardContent className="space-y-6">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <p className="text-xl text-white/90 leading-relaxed italic">
                  "{testimonials[activeTestimonial].content}"
                </p>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 gradient-bg rounded-full flex items-center justify-center">
                    <span className="text-white font-bold">
                      {testimonials[activeTestimonial].name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="text-white font-medium">{testimonials[activeTestimonial].name}</div>
                    <div className="text-white/60 text-sm">{testimonials[activeTestimonial].company} • {testimonials[activeTestimonial].role}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial navigation */}
            <div className="flex justify-center gap-2 mt-8">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === activeTestimonial ? 'bg-blue-400' : 'bg-white/20'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="stat-card p-12">
            <CardContent className="space-y-8">
              <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
                Klaar om je
                <span className="gradient-text block">Bouwbedrijf te transformeren?</span>
              </h2>
              
              <p className="text-xl text-white/80 max-w-2xl mx-auto">
                Sluit je aan bij duizenden tevreden bouwbedrijven die al de kracht van AI ervaren.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  onClick={handleGetStarted}
                  size="lg"
                  className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105 text-lg px-8 py-4"
                >
                  Start Je Gratis Proefperiode
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
                <Button 
                  variant="outline" 
                  size="lg"
                  className="border-white/20 text-white hover:bg-white/10 text-lg px-8 py-4"
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Plan Demo
                </Button>
              </div>

              <div className="flex items-center justify-center gap-6 text-sm text-white/60">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  Geen creditcard nodig
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  14 dagen gratis proefperiode
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  Op elk moment opzegbaar
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
                Neem
                <span className="gradient-text block">Contact op</span>
              </h2>
              <p className="text-xl text-white/80 mb-8">
                Heb je vragen? We staan klaar om je te helpen met het vinden van de perfecte oplossing voor jouw bedrijf.
              </p>
              
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 gradient-bg rounded-xl flex items-center justify-center">
                    <Mail className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Email</div>
                    <div className="text-white/60"><EMAIL></div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 gradient-bg rounded-xl flex items-center justify-center">
                    <Phone className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Telefoon</div>
                    <div className="text-white/60">+31 85 888 8888</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 gradient-bg rounded-xl flex items-center justify-center">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">Kantoor</div>
                    <div className="text-white/60">Amsterdam, Nederland</div>
                  </div>
                </div>
              </div>
            </div>

            <Card className="stat-card">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Enterprise Demo Aanvragen</h3>
                <form className="space-y-4">
                  <div>
                    <input
                      type="text"
                      placeholder="Bedrijfsnaam"
                      className="ai-chat-input w-full"
                    />
                  </div>
                  <div>
                    <input
                      type="email"
                      placeholder="Email adres"
                      className="ai-chat-input w-full"
                    />
                  </div>
                  <div>
                    <input
                      type="tel"
                      placeholder="Telefoonnummer"
                      className="ai-chat-input w-full"
                    />
                  </div>
                  <div>
                    <textarea
                      placeholder="Beschrijf je behoeften"
                      rows={4}
                      className="ai-chat-input w-full resize-none"
                    ></textarea>
                  </div>
                  <Button className="w-full gradient-bg hover:shadow-lg transition-all duration-300">
                    Plan Enterprise Demo
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 gradient-bg rounded-xl flex items-center justify-center glow-effect">
                  <Sparkles className="h-6 w-6 text-white" />
                </div>
                <span className="text-white font-bold text-xl">Quote.AI+CRM</span>
              </div>
              <p className="text-white/60 text-sm">
                De toekomst van bouwmanagement is hier.
              </p>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">Product</h4>
              <ul className="space-y-2 text-sm text-white/60">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Prijzen</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integraties</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">Bedrijf</h4>
              <ul className="space-y-2 text-sm text-white/60">
                <li><a href="#" className="hover:text-white transition-colors">Over ons</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-white/60">
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Status</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-white/10 pt-8 flex flex-col sm:flex-row justify-between items-center">
            <p className="text-white/60 text-sm mb-4 sm:mb-0">
              © 2024 Quote.AI+CRM. All rights reserved.
            </p>
            <div className="flex gap-6">
              <a href="#" className="text-white/60 hover:text-white transition-colors text-sm">
                Privacy Policy
              </a>
              <a href="#" className="text-white/60 hover:text-white transition-colors text-sm">
                Terms of Service
              </a>
              <a href="#" className="text-white/60 hover:text-white transition-colors text-sm">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}