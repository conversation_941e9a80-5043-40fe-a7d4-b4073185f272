import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { WhatsAppService, MessageLogService, getMessagingConfig } from '@/lib/messaging-utils'
import { z } from 'zod'

const sendWhatsAppSchema = z.object({
  to: z.string().min(10, 'Valid phone number is required'),
  message: z.string().min(1, 'Message is required'),
  type: z.enum(['text', 'template']).default('text'),
  templateName: z.string().optional(),
  templateParameters: z.array(z.string()).optional(),
  customerId: z.string().cuid().optional(),
  quoteId: z.string().cuid().optional()
})

export async function POST(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(30, 15 * 60 * 1000), // 30 WhatsApp messages per 15 minutes
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate input
    const body = await request.json()
    const validationResult = sendWhatsAppSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { to, message, type, templateName, templateParameters, customerId, quoteId } = validationResult.data

    // Get messaging configuration
    const config = await getMessagingConfig(organizationId)
    
    if (!config.whatsapp) {
      return NextResponse.json(
        { error: 'WhatsApp configuration not found. Please configure WhatsApp Business API settings.' },
        { status: 400 }
      )
    }

    // Validate customer if provided
    if (customerId) {
      const customer = await db.customer.findFirst({
        where: { id: customerId, organizationId }
      })
      if (!customer) {
        return NextResponse.json(
          { error: 'Customer not found' },
          { status: 404 }
        )
      }
    }

    // Validate quote if provided
    if (quoteId) {
      const quote = await db.quote.findFirst({
        where: { id: quoteId, organizationId }
      })
      if (!quote) {
        return NextResponse.json(
          { error: 'Quote not found' },
          { status: 404 }
        )
      }
    }

    // Initialize WhatsApp service
    const whatsappService = new WhatsAppService(config.whatsapp)

    let result
    if (type === 'template' && templateName) {
      result = await whatsappService.sendTemplate(to, templateName, templateParameters || [])
    } else {
      result = await whatsappService.sendMessage(to, message)
    }

    if (!result.success) {
      return NextResponse.json(
        { error: `Failed to send WhatsApp message: ${result.error}` },
        { status: 500 }
      )
    }

    // Log message
    const messageLog = await MessageLogService.logMessage(
      organizationId,
      {
        type: 'WHATSAPP',
        direction: 'OUTBOUND',
        content: message,
        phoneNumber: to,
        customerId,
        quoteId,
        metadata: {
          messageId: result.messageId,
          type,
          templateName,
          templateParameters
        }
      },
      session.user.id
    )

    return NextResponse.json({
      success: true,
      messageId: result.messageId,
      logId: messageLog.id
    })

  } catch (error) {
    console.error('WhatsApp send error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    const { searchParams } = new URL(request.url)
    const customerId = searchParams.get('customerId')
    const quoteId = searchParams.get('quoteId')
    const limit = parseInt(searchParams.get('limit') || '50')

    const messages = await MessageLogService.getMessageHistory(organizationId, {
      customerId: customerId || undefined,
      quoteId: quoteId || undefined,
      type: 'WHATSAPP',
      limit
    })

    return NextResponse.json({
      data: messages,
      total: messages.length
    })

  } catch (error) {
    console.error('Error fetching WhatsApp history:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Webhook endpoint for receiving WhatsApp messages
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Get webhook verify token from environment
    const verifyToken = process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'default-verify-token'
    
    // Parse incoming messages
    const whatsappService = new WhatsAppService({
      token: '', // Not needed for parsing
      phoneId: '',
      businessId: '',
      webhookVerifyToken: verifyToken
    })
    
    const messages = whatsappService.parseWebhookMessage(body)
    
    // Process each incoming message
    for (const message of messages) {
      try {
        // Find customer by phone number
        const customer = await db.customer.findFirst({
          where: {
            phone: {
              contains: message.from
            }
          },
          include: {
            organization: true
          }
        })

        if (customer) {
          // Log incoming message
          await MessageLogService.logMessage(
            customer.organizationId,
            {
              type: 'WHATSAPP',
              direction: 'INBOUND',
              content: message.text,
              phoneNumber: message.from,
              customerId: customer.id,
              metadata: {
                whatsappMessageId: message.id,
                timestamp: message.timestamp,
                messageType: message.type,
                contacts: message.contacts
              }
            }
          )

          // Create activity for the customer
          await db.activity.create({
            data: {
              type: 'WHATSAPP',
              title: 'WhatsApp message received',
              description: `Message: ${message.text.substring(0, 100)}${message.text.length > 100 ? '...' : ''}`,
              completed: false,
              organizationId: customer.organizationId,
              customerId: customer.id,
              metadata: JSON.stringify({
                whatsappMessageId: message.id,
                phoneNumber: message.from
              })
            }
          })
        }
      } catch (error) {
        console.error('Error processing WhatsApp message:', error)
      }
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('WhatsApp webhook error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
