import { NextRequest, NextResponse } from 'next/server'
import { WhatsAppService } from '@/lib/messaging-utils'

// Webhook verification for WhatsApp
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const mode = searchParams.get('hub.mode')
    const token = searchParams.get('hub.verify_token')
    const challenge = searchParams.get('hub.challenge')

    if (!mode || !token || !challenge) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // Get verify token from environment
    const verifyToken = process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || 'default-verify-token'
    
    const whatsappService = new WhatsAppService({
      token: '', // Not needed for verification
      phoneId: '',
      businessId: '',
      webhookVerifyToken: verifyToken
    })

    const result = whatsappService.verifyWebhook(mode, token, challenge)
    
    if (result) {
      return new NextResponse(result, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain'
        }
      })
    } else {
      return NextResponse.json(
        { error: 'Verification failed' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('WhatsApp webhook verification error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle incoming WhatsApp messages
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Forward to main WhatsApp route for processing
    const response = await fetch(`${request.nextUrl.origin}/api/messaging/whatsapp`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    })

    if (response.ok) {
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json(
        { error: 'Failed to process webhook' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('WhatsApp webhook processing error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
