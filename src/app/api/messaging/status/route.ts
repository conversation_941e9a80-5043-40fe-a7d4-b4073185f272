import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { EmailService, WhatsAppService, getMessagingConfig } from '@/lib/messaging-utils'

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(50, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Get messaging configuration
    const config = await getMessagingConfig(organizationId)

    // Check email configuration and connection
    let emailStatus = {
      configured: false,
      connected: false,
      error: null as string | null
    }

    if (config.email) {
      emailStatus.configured = true
      try {
        const emailService = new EmailService(config.email)
        emailStatus.connected = await emailService.verifyConnection()
      } catch (error) {
        emailStatus.error = error instanceof Error ? error.message : 'Connection failed'
      }
    }

    // Check WhatsApp configuration
    let whatsappStatus = {
      configured: false,
      error: null as string | null
    }

    if (config.whatsapp) {
      whatsappStatus.configured = true
      // Note: WhatsApp doesn't have a simple connection test like SMTP
      // We just check if the configuration is present
    }

    // Get recent message statistics
    const now = new Date()
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

    const [
      emailStats24h,
      emailStats7d,
      emailStats30d,
      whatsappStats24h,
      whatsappStats7d,
      whatsappStats30d,
      totalMessages,
      failedMessages
    ] = await Promise.all([
      // Email stats
      db.messageLog.count({
        where: {
          organizationId,
          type: 'EMAIL',
          createdAt: { gte: last24Hours }
        }
      }),
      db.messageLog.count({
        where: {
          organizationId,
          type: 'EMAIL',
          createdAt: { gte: last7Days }
        }
      }),
      db.messageLog.count({
        where: {
          organizationId,
          type: 'EMAIL',
          createdAt: { gte: last30Days }
        }
      }),
      // WhatsApp stats
      db.messageLog.count({
        where: {
          organizationId,
          type: 'WHATSAPP',
          createdAt: { gte: last24Hours }
        }
      }),
      db.messageLog.count({
        where: {
          organizationId,
          type: 'WHATSAPP',
          createdAt: { gte: last7Days }
        }
      }),
      db.messageLog.count({
        where: {
          organizationId,
          type: 'WHATSAPP',
          createdAt: { gte: last30Days }
        }
      }),
      // Total and failed messages
      db.messageLog.count({
        where: { organizationId }
      }),
      db.messageLog.count({
        where: {
          organizationId,
          status: 'FAILED'
        }
      })
    ])

    // Get recent message activity
    const recentMessages = await db.messageLog.findMany({
      where: { organizationId },
      include: {
        customer: {
          select: { name: true, company: true }
        },
        user: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    // Get template count
    const templateCount = await db.emailTemplate.count({
      where: { organizationId }
    })

    return NextResponse.json({
      email: emailStatus,
      whatsapp: whatsappStatus,
      statistics: {
        email: {
          last24Hours: emailStats24h,
          last7Days: emailStats7d,
          last30Days: emailStats30d
        },
        whatsapp: {
          last24Hours: whatsappStats24h,
          last7Days: whatsappStats7d,
          last30Days: whatsappStats30d
        },
        total: {
          messages: totalMessages,
          failed: failedMessages,
          successRate: totalMessages > 0 ? ((totalMessages - failedMessages) / totalMessages * 100).toFixed(1) : '0'
        }
      },
      templates: {
        count: templateCount
      },
      recentActivity: recentMessages
    })

  } catch (error) {
    console.error('Error fetching messaging status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Test messaging configuration
export async function POST(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(10, 15 * 60 * 1000), // Limited test requests
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    const { type } = await request.json()

    if (!type || !['email', 'whatsapp'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid test type. Must be "email" or "whatsapp"' },
        { status: 400 }
      )
    }

    const config = await getMessagingConfig(organizationId)

    if (type === 'email') {
      if (!config.email) {
        return NextResponse.json(
          { error: 'Email configuration not found' },
          { status: 400 }
        )
      }

      const emailService = new EmailService(config.email)
      const isConnected = await emailService.verifyConnection()

      if (isConnected) {
        // Send test email to user
        const result = await emailService.sendEmail(
          session.user.email!,
          'Test Email - Quote.AI+CRM',
          'This is a test email to verify your email configuration is working correctly.',
          {
            html: `
              <h2>Test Email - Quote.AI+CRM</h2>
              <p>This is a test email to verify your email configuration is working correctly.</p>
              <p><strong>Sent at:</strong> ${new Date().toLocaleString('nl-NL')}</p>
              <p><strong>Organization:</strong> ${session.user.organizationName}</p>
            `
          }
        )

        return NextResponse.json({
          success: result.success,
          message: result.success 
            ? 'Test email sent successfully' 
            : `Failed to send test email: ${result.error}`
        })
      } else {
        return NextResponse.json({
          success: false,
          message: 'Failed to connect to email server'
        })
      }
    }

    if (type === 'whatsapp') {
      if (!config.whatsapp) {
        return NextResponse.json(
          { error: 'WhatsApp configuration not found' },
          { status: 400 }
        )
      }

      // For WhatsApp, we can't easily send a test message without a valid phone number
      // So we just verify the configuration is present
      return NextResponse.json({
        success: true,
        message: 'WhatsApp configuration is present. To test messaging, send a message to a customer.'
      })
    }

  } catch (error) {
    console.error('Error testing messaging configuration:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
