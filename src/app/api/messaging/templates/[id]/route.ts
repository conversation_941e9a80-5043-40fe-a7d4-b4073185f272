import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { updateEmailTemplateSchema, idParamSchema } from '@/lib/validations'

interface RouteParams {
  params: { id: string }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      )
    }

    const template = await db.emailTemplate.findFirst({
      where: {
        id: params.id,
        organizationId
      }
    })

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(template)

  } catch (error) {
    console.error('Error fetching email template:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(50, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      )
    }

    // Validate input
    const body = await request.json()
    const validationResult = updateEmailTemplateSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    // Check if template exists and belongs to organization
    const existingTemplate = await db.emailTemplate.findFirst({
      where: {
        id: params.id,
        organizationId
      }
    })

    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      )
    }

    const { name, subject, content, templateType, isDefault } = validationResult.data

    // Check if name is being changed and if it conflicts with another template
    if (name && name !== existingTemplate.name) {
      const nameConflict = await db.emailTemplate.findFirst({
        where: {
          organizationId,
          name,
          id: { not: params.id }
        }
      })

      if (nameConflict) {
        return NextResponse.json(
          { error: 'Another template with this name already exists' },
          { status: 400 }
        )
      }
    }

    // If setting as default, unset other defaults of the same type
    if (isDefault && (templateType || existingTemplate.templateType)) {
      const typeToCheck = templateType || existingTemplate.templateType
      await db.emailTemplate.updateMany({
        where: {
          organizationId,
          templateType: typeToCheck,
          isDefault: true,
          id: { not: params.id }
        },
        data: {
          isDefault: false
        }
      })
    }

    const updatedTemplate = await db.emailTemplate.update({
      where: { id: params.id },
      data: {
        ...(name && { name }),
        ...(subject && { subject }),
        ...(content && { content }),
        ...(templateType && { templateType }),
        ...(isDefault !== undefined && { isDefault })
      }
    })

    return NextResponse.json(updatedTemplate)

  } catch (error) {
    console.error('Error updating email template:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(25, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      )
    }

    // Check if template exists and belongs to organization
    const existingTemplate = await db.emailTemplate.findFirst({
      where: {
        id: params.id,
        organizationId
      }
    })

    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      )
    }

    // Delete template
    await db.emailTemplate.delete({
      where: { id: params.id }
    })

    return NextResponse.json(
      { message: 'Template deleted successfully' },
      { status: 200 }
    )

  } catch (error) {
    console.error('Error deleting email template:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
