import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { createEmailTemplateSchema, updateEmailTemplateSchema, paginationSchema } from '@/lib/validations'

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    const { page, limit, search, sortBy, sortOrder } = paginationSchema.parse(queryParams)

    // Build where clause
    const where: any = { organizationId }
    
    // Filter by template type
    const templateType = searchParams.get('templateType')
    if (templateType && ['QUOTE', 'INVOICE', 'REMINDER', 'WELCOME'].includes(templateType)) {
      where.templateType = templateType
    }
    
    // Search functionality
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { subject: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Build order by clause
    const orderBy: any = { createdAt: sortOrder }
    if (sortBy && ['name', 'subject', 'templateType', 'createdAt'].includes(sortBy)) {
      orderBy = { [sortBy]: sortOrder }
    }

    // Get total count for pagination
    const total = await db.emailTemplate.count({ where })

    // Get templates with pagination
    const templates = await db.emailTemplate.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit
    })

    return NextResponse.json({
      data: templates,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching email templates:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(20, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate input
    const body = await request.json()
    const validationResult = createEmailTemplateSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { name, subject, content, templateType, isDefault } = validationResult.data

    // Check if template name already exists in organization
    const existingTemplate = await db.emailTemplate.findFirst({
      where: {
        organizationId,
        name
      }
    })

    if (existingTemplate) {
      return NextResponse.json(
        { error: 'Template with this name already exists' },
        { status: 400 }
      )
    }

    // If setting as default, unset other defaults of the same type
    if (isDefault && templateType) {
      await db.emailTemplate.updateMany({
        where: {
          organizationId,
          templateType,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      })
    }

    const template = await db.emailTemplate.create({
      data: {
        name,
        subject,
        content,
        templateType,
        isDefault,
        organizationId
      }
    })

    return NextResponse.json(template, { status: 201 })

  } catch (error) {
    console.error('Error creating email template:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
