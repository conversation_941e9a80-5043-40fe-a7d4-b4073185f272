import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { EmailService, MessageLogService, getMessagingConfig } from '@/lib/messaging-utils'
import { z } from 'zod'

const sendEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  html: z.string().optional(),
  customerId: z.string().cuid().optional(),
  quoteId: z.string().cuid().optional(),
  templateId: z.string().cuid().optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    content: z.string(),
    contentType: z.string().optional()
  })).optional()
})

export async function POST(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(20, 15 * 60 * 1000), // 20 emails per 15 minutes
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate input
    const body = await request.json()
    const validationResult = sendEmailSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { to, subject, content, html, customerId, quoteId, templateId, attachments } = validationResult.data

    // Get messaging configuration
    const config = await getMessagingConfig(organizationId)
    
    if (!config.email) {
      return NextResponse.json(
        { error: 'Email configuration not found. Please configure SMTP settings.' },
        { status: 400 }
      )
    }

    // If template is specified, load and process it
    let finalSubject = subject
    let finalContent = content
    let finalHtml = html

    if (templateId) {
      const template = await db.emailTemplate.findFirst({
        where: {
          id: templateId,
          organizationId
        }
      })

      if (!template) {
        return NextResponse.json(
          { error: 'Email template not found' },
          { status: 404 }
        )
      }

      finalSubject = template.subject
      finalContent = template.content
      finalHtml = template.content // Assuming template content is HTML
    }

    // Get customer and quote data for template variables
    let templateData: any = {}
    
    if (customerId) {
      const customer = await db.customer.findFirst({
        where: { id: customerId, organizationId }
      })
      if (customer) {
        templateData.customer = customer
      }
    }

    if (quoteId) {
      const quote = await db.quote.findFirst({
        where: { id: quoteId, organizationId },
        include: {
          customer: true,
          items: true
        }
      })
      if (quote) {
        templateData.quote = quote
      }
    }

    // Process template variables
    finalSubject = processTemplateVariables(finalSubject, templateData)
    finalContent = processTemplateVariables(finalContent, templateData)
    if (finalHtml) {
      finalHtml = processTemplateVariables(finalHtml, templateData)
    }

    // Initialize email service
    const emailService = new EmailService(config.email)

    // Verify connection
    const isConnected = await emailService.verifyConnection()
    if (!isConnected) {
      return NextResponse.json(
        { error: 'Failed to connect to email server. Please check SMTP configuration.' },
        { status: 500 }
      )
    }

    // Send email
    const result = await emailService.sendEmail(to, finalSubject, finalContent, {
      html: finalHtml,
      attachments
    })

    if (!result.success) {
      return NextResponse.json(
        { error: `Failed to send email: ${result.error}` },
        { status: 500 }
      )
    }

    // Log message
    const messageLog = await MessageLogService.logMessage(
      organizationId,
      {
        type: 'EMAIL',
        direction: 'OUTBOUND',
        subject: finalSubject,
        content: finalContent,
        fromAddress: config.email.from,
        toAddress: Array.isArray(to) ? to.join(', ') : to,
        customerId,
        quoteId,
        metadata: {
          messageId: result.messageId,
          templateId,
          attachments: attachments?.map(a => a.filename)
        }
      },
      session.user.id
    )

    return NextResponse.json({
      success: true,
      messageId: result.messageId,
      logId: messageLog.id
    })

  } catch (error) {
    console.error('Email send error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    const { searchParams } = new URL(request.url)
    const customerId = searchParams.get('customerId')
    const quoteId = searchParams.get('quoteId')
    const limit = parseInt(searchParams.get('limit') || '50')

    const messages = await MessageLogService.getMessageHistory(organizationId, {
      customerId: customerId || undefined,
      quoteId: quoteId || undefined,
      type: 'EMAIL',
      limit
    })

    return NextResponse.json({
      data: messages,
      total: messages.length
    })

  } catch (error) {
    console.error('Error fetching email history:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Process template variables like {{customer.name}}, {{quote.number}}
function processTemplateVariables(template: string, data: any): string {
  let processed = template

  // Replace customer variables
  if (data.customer) {
    processed = processed.replace(/\{\{customer\.name\}\}/g, data.customer.name || '')
    processed = processed.replace(/\{\{customer\.company\}\}/g, data.customer.company || '')
    processed = processed.replace(/\{\{customer\.email\}\}/g, data.customer.email || '')
    processed = processed.replace(/\{\{customer\.phone\}\}/g, data.customer.phone || '')
  }

  // Replace quote variables
  if (data.quote) {
    processed = processed.replace(/\{\{quote\.number\}\}/g, data.quote.number || '')
    processed = processed.replace(/\{\{quote\.title\}\}/g, data.quote.title || '')
    processed = processed.replace(/\{\{quote\.total\}\}/g, `€${data.quote.totaalPrijs?.toFixed(2) || '0.00'}`)
    processed = processed.replace(/\{\{quote\.status\}\}/g, data.quote.status || '')
  }

  // Replace date variables
  const now = new Date()
  processed = processed.replace(/\{\{date\.today\}\}/g, now.toLocaleDateString('nl-NL'))
  processed = processed.replace(/\{\{date\.now\}\}/g, now.toLocaleString('nl-NL'))

  return processed
}
