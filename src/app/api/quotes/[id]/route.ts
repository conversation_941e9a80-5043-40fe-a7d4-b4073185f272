import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { updateQuoteSchema, idParamSchema } from '@/lib/validations'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { processQuoteItems, isValidStatusTransition } from '@/lib/quote-utils'

interface RouteParams {
  params: { id: string }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)

  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid quote ID' },
        { status: 400 }
      )
    }

    const quote = await db.quote.findFirst({
      where: {
        id: params.id,
        organizationId
      },
      include: {
        user: {
          select: { name: true, email: true }
        },
        customer: {
          select: {
            id: true,
            name: true,
            company: true,
            email: true,
            phone: true,
            address: true
          }
        },
        property: {
          select: {
            id: true,
            name: true,
            address: true,
            propertyType: true,
            sizeM2: true
          }
        },
        items: {
          orderBy: { volgorde: 'asc' }
        },
        activities: {
          select: {
            id: true,
            type: true,
            title: true,
            description: true,
            completed: true,
            createdAt: true,
            user: {
              select: { name: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 20
        },
        _count: {
          select: {
            items: true,
            activities: true
          }
        }
      }
    })

    if (!quote) {
      return NextResponse.json(
        { error: 'Quote not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(quote)

  } catch (error) {
    console.error('Error fetching quote:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quoteId = params.id
    const organizationId = session.user.organizationId
    const {
      title,
      description,
      customerId,
      propertyId,
      status,
      basisPrijs,
      btwPercentage,
      btwBedrag,
      totaalPrijs,
      items
    } = await request.json()

    // Check if quote exists and belongs to organization
    const existingQuote = await db.quote.findFirst({
      where: {
        id: quoteId,
        organizationId
      }
    })

    if (!existingQuote) {
      return NextResponse.json({ error: 'Quote not found' }, { status: 404 })
    }

    // Update quote
    const updatedQuote = await db.quote.update({
      where: { id: quoteId },
      data: {
        title,
        description,
        customerId,
        propertyId,
        status,
        basisPrijs,
        btwPercentage,
        btwBedrag,
        totaalPrijs
      },
      include: {
        user: {
          select: { name: true }
        },
        customer: true,
        property: true,
        items: {
          orderBy: { volgorde: 'asc' }
        }
      }
    })

    // Update quote items if provided
    if (items && Array.isArray(items)) {
      // Delete existing items
      await db.quoteItem.deleteMany({
        where: { quoteId }
      })

      // Create new items
      await db.quoteItem.createMany({
        data: items.map((item: any, index: number) => ({
          quoteId,
          beschrijving: item.beschrijving || '',
          aantal: item.aantal || 1,
          eenheid: item.eenheid || 'stuk',
          eenheidPrijs: item.eenheidPrijs || 0,
          totaalPrijs: item.totaalPrijs || 0,
          volgorde: index + 1
        }))
      })
    }

    // Create activity
    await db.activity.create({
      data: {
        type: 'QUOTE',
        title: 'Updated quote',
        description: `Quote "${title}" was updated`,
        completed: true,
        organizationId,
        userId: session.user.id,
        customerId,
        quoteId
      }
    })

    const quoteWithItems = await db.quote.findUnique({
      where: { id: quoteId },
      include: {
        user: {
          select: { name: true }
        },
        customer: true,
        property: true,
        items: {
          orderBy: { volgorde: 'asc' }
        }
      }
    })

    return NextResponse.json(quoteWithItems)

  } catch (error) {
    console.error('Error updating quote:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quoteId = params.id
    const organizationId = session.user.organizationId

    // Check if quote exists and belongs to organization
    const existingQuote = await db.quote.findFirst({
      where: {
        id: quoteId,
        organizationId
      }
    })

    if (!existingQuote) {
      return NextResponse.json({ error: 'Quote not found' }, { status: 404 })
    }

    // Delete quote items first (foreign key constraint)
    await db.quoteItem.deleteMany({
      where: { quoteId }
    })

    // Delete quote
    await db.quote.delete({
      where: { id: quoteId }
    })

    // Create activity
    await db.activity.create({
      data: {
        type: 'QUOTE',
        title: 'Deleted quote',
        description: `Quote was deleted`,
        completed: true,
        organizationId,
        userId: session.user.id
      }
    })

    return NextResponse.json({ message: 'Quote deleted successfully' })

  } catch (error) {
    console.error('Error deleting quote:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}