/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable */
import React from 'react'
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { renderToBuffer } from '@react-pdf/renderer'
import { Document, Page, Text, View, StyleSheet, Font, Image } from '@react-pdf/renderer'

// Register fonts
Font.register({
  family: 'Inter',
  src: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZg.woff2',
})

const styles = StyleSheet.create({
  page: {
    padding: 50,
    backgroundColor: '#ffffff',
    fontFamily: 'Inter',
  },
  header: {
    marginBottom: 30,
    borderBottom: '2px solid #e5e7eb',
    paddingBottom: 20,
  },
  companyInfo: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 5,
  },
  quoteInfo: {
    backgroundColor: '#f3f4f6',
    padding: 15,
    borderRadius: 8,
    marginBottom: 30,
  },
  quoteInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  quoteInfoLabel: {
    fontSize: 12,
    color: '#6b7280',
  },
  quoteInfoValue: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 15,
    borderBottom: '1 solid #e5e7eb',
    paddingBottom: 5,
  },
  customerInfo: {
    marginBottom: 20,
  },
  customerInfoRow: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  customerInfoLabel: {
    fontSize: 12,
    color: '#6b7280',
    width: 80,
  },
  customerInfoValue: {
    fontSize: 12,
    color: '#1f2937',
    flex: 1,
  },
  table: {
    width: '100%',
    borderCollapse: 'collapse',
    marginBottom: 20,
  },
  tableHeader: {
    backgroundColor: '#f3f4f6',
    padding: 10,
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1f2937',
    border: '1px solid #e5e7eb',
  },
  tableCell: {
    padding: 10,
    fontSize: 12,
    color: '#1f2937',
    border: '1px solid #e5e7eb',
  },
  tableCellRight: {
    padding: 10,
    fontSize: 12,
    color: '#1f2937',
    border: '1px solid #e5e7eb',
    textAlign: 'right' as const,
  },
  totalSection: {
    backgroundColor: '#f3f4f6',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  totalLabel: {
    fontSize: 12,
    color: '#6b7280',
  },
  totalValue: {
    fontSize: 12,
    color: '#1f2937',
  },
  grandTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTop: '2 solid #1f2937',
    paddingTop: 10,
    marginTop: 10,
  },
  grandTotalLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  grandTotalValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 50,
    right: 50,
    fontSize: 10,
    color: '#6b7280',
    textAlign: 'center',
    borderTop: '1px solid #e5e7eb',
    paddingTop: 10,
  },
})

interface QuotePDFProps {
  quote: any
}

function QuotePDF({ quote }: QuotePDFProps) {
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
          <Text style={styles.title}>Quote</Text>
          <Text style={styles.subtitle}>Quote #{quote.number}</Text>
          
          <View style={styles.companyInfo}>
            <Text style={styles.subtitle}>{quote.user?.organization?.name || 'Your Company'}</Text>
            <Text style={styles.subtitle}>{quote.user?.organization?.phone || ''}</Text>
          </View>
        </View>

        <View style={styles.quoteInfo}>
          <View style={styles.quoteInfoRow}>
            <Text style={styles.quoteInfoLabel}>Quote Number:</Text>
            <Text style={styles.quoteInfoValue}>{quote.number}</Text>
          </View>
          <View style={styles.quoteInfoRow}>
            <Text style={styles.quoteInfoLabel}>Date:</Text>
            <Text style={styles.quoteInfoValue}>
              {new Date(quote.createdAt).toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.quoteInfoRow}>
            <Text style={styles.quoteInfoLabel}>Status:</Text>
            <Text style={styles.quoteInfoValue}>{quote.status}</Text>
          </View>
          <View style={styles.quoteInfoRow}>
            <Text style={styles.quoteInfoLabel}>Valid Until:</Text>
            <Text style={styles.quoteInfoValue}>
              {quote.expiresAt 
                ? new Date(quote.expiresAt).toLocaleDateString()
                : '30 days from issue date'
              }
            </Text>
          </View>
        </View>

        {/* Customer Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Customer Information</Text>
          <View style={styles.customerInfo}>
            <View style={styles.customerInfoRow}>
              <Text style={styles.customerInfoLabel}>Name:</Text>
              <Text style={styles.customerInfoValue}>{quote.customer?.name}</Text>
            </View>
            {quote.customer?.company && (
              <View style={styles.customerInfoRow}>
                <Text style={styles.customerInfoLabel}>Company:</Text>
                <Text style={styles.customerInfoValue}>{quote.customer.company}</Text>
              </View>
            )}
            {quote.customer?.email && (
              <View style={styles.customerInfoRow}>
                <Text style={styles.customerInfoLabel}>Email:</Text>
                <Text style={styles.customerInfoValue}>{quote.customer.email}</Text>
              </View>
            )}
            {quote.customer?.phone && (
              <View style={styles.customerInfoRow}>
                <Text style={styles.customerInfoLabel}>Phone:</Text>
                <Text style={styles.customerInfoValue}>{quote.customer.phone}</Text>
              </View>
            )}
          </View>
        </View>

        {/* Property Information */}
        {quote.property && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Property Information</Text>
            <View style={styles.customerInfo}>
              <View style={styles.customerInfoRow}>
                <Text style={styles.customerInfoLabel}>Name:</Text>
                <Text style={styles.customerInfoValue}>{quote.property.name}</Text>
              </View>
              <View style={styles.customerInfoRow}>
                <Text style={styles.customerInfoLabel}>Address:</Text>
                <Text style={styles.customerInfoValue}>{quote.property.address}</Text>
              </View>
              {quote.property.propertyType && (
                <View style={styles.customerInfoRow}>
                  <Text style={styles.customerInfoLabel}>Type:</Text>
                  <Text style={styles.customerInfoValue}>{quote.property.propertyType}</Text>
                </View>
              )}
              {quote.property.sizeM2 && (
                <View style={styles.customerInfoRow}>
                  <Text style={styles.customerInfoLabel}>Size:</Text>
                  <Text style={styles.customerInfoValue}>{quote.property.sizeM2} m²</Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Quote Description */}
        {quote.description && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.customerInfoValue}>{quote.description}</Text>
          </View>
        )}

        {/* Quote Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quote Items</Text>
          
          {/* Table Header */}
          <View style={styles.table}>
            <View style={{ flexDirection: 'row' }}>
              <View style={[styles.tableHeader, { flex: 3 }]}>Description</View>
              <View style={[styles.tableHeader, { flex: 1 }]}>Quantity</View>
              <View style={[styles.tableHeader, { flex: 1 }]}>Unit</View>
              <View style={[styles.tableHeader, { flex: 1, textAlign: 'right' as const }]}>Unit Price</View>
              <View style={[styles.tableHeader, { flex: 1, textAlign: 'right' as const }]}>Total</View>
            </View>
            
            {/* Table Rows */}
            {quote.items?.map((item: any, index: number) => (
              <View key={index} style={{ flexDirection: 'row' }}>
                <View style={[styles.tableCell, { flex: 3 }]}>{item.beschrijving}</View>
                <View style={[styles.tableCell, { flex: 1 }]}>{item.aantal}</View>
                <View style={[styles.tableCell, { flex: 1 }]}>{item.eenheid}</View>
                <View style={[styles.tableCellRight, { flex: 1 }]}>€{item.eenheidPrijs.toFixed(2)}</View>
                <View style={[styles.tableCellRight, { flex: 1 }]}>€{item.totaalPrijs.toFixed(2)}</View>
              </View>
            ))}
          </View>
        </View>

        {/* Total Section */}
        <View style={styles.totalSection}>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Subtotal:</Text>
            <Text style={styles.totalValue}>€{quote.basisPrijs?.toFixed(2) || '0.00'}</Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>VAT ({quote.btwPercentage || 21}%):</Text>
            <Text style={styles.totalValue}>€{quote.btwBedrag?.toFixed(2) || '0.00'}</Text>
          </View>
          <View style={styles.grandTotal}>
            <Text style={styles.grandTotalLabel}>Total:</Text>
            <Text style={styles.grandTotalValue}>€{quote.totaalPrijs?.toFixed(2) || '0.00'}</Text>
          </View>
        </View>

        {/* Footer */}
        <Text style={styles.footer}>
          This quote is valid for 30 days from the date of issue. 
          Please contact us if you have any questions.
        </Text>
      </Page>
    </Document>
  )
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const quoteId = params.id
    const organizationId = session.user.organizationId

    // Get quote with all related data
    const quote = await db.quote.findFirst({
      where: {
        id: quoteId,
        organizationId
      },
      include: {
        user: {
          include: {
            organization: true
          }
        },
        customer: true,
        property: true,
        items: {
          orderBy: { volgorde: 'asc' }
        }
      }
    })

    if (!quote) {
      return NextResponse.json({ error: 'Quote not found' }, { status: 404 })
    }

    // Generate PDF
    const pdfBuffer = await renderToBuffer(<QuotePDF quote={quote} />)

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="quote-${quote.number}.pdf"`,
      },
    })

  } catch (error) {
    console.error('Error generating PDF:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}