import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { createQuoteSchema, paginationSchema } from '@/lib/validations'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { generateQuoteNumber, processQuoteItems, generateExpiryDate } from '@/lib/quote-utils'

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)

  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    const { page, limit, search, sortBy, sortOrder } = paginationSchema.parse(queryParams)

    // Build where clause
    const where: any = { organizationId }

    // Filter by status
    const status = searchParams.get('status')
    if (status && ['DRAFT', 'SENT', 'ACCEPTED', 'REJECTED', 'EXPIRED'].includes(status)) {
      where.status = status
    }

    // Filter by customer
    const customerId = searchParams.get('customerId')
    if (customerId) {
      where.customerId = customerId
    }

    // Search functionality
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { number: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    // Build order by clause
    const orderBy: any = { createdAt: sortOrder }
    if (sortBy && ['number', 'title', 'status', 'totaalPrijs', 'createdAt', 'sentAt'].includes(sortBy)) {
      orderBy = { [sortBy]: sortOrder }
    }

    // Get total count for pagination
    const total = await db.quote.count({ where })

    // Get quotes with pagination
    const quotes = await db.quote.findMany({
      where,
      include: {
        user: {
          select: { name: true }
        },
        customer: {
          select: { id: true, name: true, company: true }
        },
        property: {
          select: { id: true, name: true, address: true }
        },
        _count: {
          select: {
            items: true,
            activities: true
          }
        }
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit
    })

    return NextResponse.json({
      data: quotes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching quotes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(25, 15 * 60 * 1000), // Lower limit for quote creation
    requireAuth
  )(request)

  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate input
    const body = await request.json()
    const validationResult = createQuoteSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { title, description, customerId, propertyId, items, btwPercentage, expiresAt } = validationResult.data

    // Verify customer exists and belongs to organization
    const customer = await db.customer.findFirst({
      where: {
        id: customerId,
        organizationId
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found or does not belong to your organization' },
        { status: 400 }
      )
    }

    // Verify property if provided
    if (propertyId) {
      const property = await db.property.findFirst({
        where: {
          id: propertyId,
          organizationId,
          customerId // Property must belong to the same customer
        }
      })

      if (!property) {
        return NextResponse.json(
          { error: 'Property not found or does not belong to the customer' },
          { status: 400 }
        )
      }
    }

    // Process quote items and calculate totals
    const { items: processedItems, totals } = processQuoteItems(items)

    // Generate unique quote number
    const quoteNumber = await generateQuoteNumber(organizationId)

    // Set expiry date
    const expiryDate = expiresAt ? new Date(expiresAt) : generateExpiryDate()

    // Create quote in transaction
    const quote = await db.$transaction(async (tx) => {
      // Create the quote
      const newQuote = await tx.quote.create({
        data: {
          number: quoteNumber,
          title,
          description,
          status: 'DRAFT',
          basisPrijs: totals.basisPrijs,
          btwPercentage: totals.btwPercentage,
          btwBedrag: totals.btwBedrag,
          totaalPrijs: totals.totaalPrijs,
          expiresAt: expiryDate,
          organizationId,
          userId: session.user.id,
          customerId,
          propertyId
        }
      })

      // Create quote items
      if (processedItems.length > 0) {
        await tx.quoteItem.createMany({
          data: processedItems.map((item, index) => ({
            quoteId: newQuote.id,
            beschrijving: item.beschrijving,
            aantal: item.aantal,
            eenheid: item.eenheid,
            eenheidPrijs: item.eenheidPrijs,
            totaalPrijs: item.aantal * item.eenheidPrijs,
            volgorde: item.volgorde || index + 1
          }))
        })
      }

      // Create activity log
      await tx.activity.create({
        data: {
          type: 'NOTE',
          title: 'Quote created',
          description: `Quote "${title}" was created`,
          completed: true,
          organizationId,
          userId: session.user.id,
          customerId,
          quoteId: newQuote.id
        }
      })

      return newQuote
    })

    // Fetch the complete quote with all relations
    const completeQuote = await db.quote.findUnique({
      where: { id: quote.id },
      include: {
        user: {
          select: { name: true }
        },
        customer: {
          select: { id: true, name: true, company: true }
        },
        property: {
          select: { id: true, name: true, address: true }
        },
        items: {
          orderBy: { volgorde: 'asc' }
        },
        _count: {
          select: {
            items: true,
            activities: true
          }
        }
      }
    })

    return NextResponse.json(completeQuote, { status: 201 })

  } catch (error) {
    console.error('Error creating quote:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}