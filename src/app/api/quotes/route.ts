import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = session.user.organizationId

    const quotes = await db.quote.findMany({
      where: { organizationId },
      include: {
        user: {
          select: { name: true }
        },
        customer: {
          select: { name: true, company: true }
        },
        property: {
          select: { name: true, address: true }
        },
        items: true
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(quotes)

  } catch (error) {
    console.error('Error fetching quotes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = session.user.organizationId
    const {
      title,
      description,
      customerId,
      propertyId,
      status,
      basisPrijs,
      btwPercentage,
      btwBedrag,
      totaalPrijs,
      items
    } = await request.json()

    if (!title || !customerId || !basisPrijs || !totaalPrijs) {
      return NextResponse.json(
        { error: 'Title, customer, base price, and total price are required' },
        { status: 400 }
      )
    }

    // Generate quote number
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    const quoteNumber = `QUOTE-${year}${month}-${random}`

    // Create quote
    const quote = await db.quote.create({
      data: {
        number: quoteNumber,
        title,
        description,
        status,
        basisPrijs,
        btwPercentage,
        btwBedrag,
        totaalPrijs,
        organizationId,
        userId: session.user.id,
        customerId,
        propertyId
      },
      include: {
        user: {
          select: { name: true }
        },
        customer: {
          select: { name: true, company: true }
        },
        property: {
          select: { name: true, address: true }
        }
      }
    })

    // Create quote items if provided
    if (items && Array.isArray(items) && items.length > 0) {
      await db.quoteItem.createMany({
        data: items.map((item: any, index: number) => ({
          quoteId: quote.id,
          beschrijving: item.beschrijving || '',
          aantal: item.aantal || 1,
          eenheid: item.eenheid || 'stuk',
          eenheidPrijs: item.eenheidPrijs || 0,
          totaalPrijs: item.totaalPrijs || 0,
          volgorde: index + 1
        }))
      })
    }

    // Create activity
    await db.activity.create({
      data: {
        type: 'QUOTE',
        title: 'Created new quote',
        description: `Quote "${title}" created for customer`,
        completed: true,
        organizationId,
        userId: session.user.id,
        customerId,
        quoteId: quote.id
      }
    })

    const quoteWithItems = await db.quote.findUnique({
      where: { id: quote.id },
      include: {
        user: {
          select: { name: true }
        },
        customer: {
          select: { name: true, company: true }
        },
        property: {
          select: { name: true, address: true }
        },
        items: {
          orderBy: { volgorde: 'asc' }
        }
      }
    })

    return NextResponse.json(quoteWithItems)

  } catch (error) {
    console.error('Error creating quote:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}