import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    let { 
      name, 
      email, 
      password, // We'll validate but not store this (NextAuth handles auth)
      organizationName, 
      organizationSlug,
      phone,
      address,
      plan = 'FREE'
    } = await request.json()
    
    // Plan normaliseren en valideren
    plan = typeof plan === 'string' ? plan.toUpperCase() : 'FREE'
    const validPlans = ['FREE', 'STARTER', 'PROFESSIONAL', 'ENTERPRISE']
    if (!validPlans.includes(plan)) {
      plan = 'FREE'
    }

    // Basic validation
    if (!name || !email || !password || !organizationName || !organizationSlug) {
      return NextResponse.json(
        { error: 'All required fields must be provided' },
        { status: 400 }
      )
    }

    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await db.profile.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Check if organization slug is already taken
    const existingSlug = await db.organization.findUnique({
      where: { slug: organizationSlug }
    })

    if (existingSlug) {
      return NextResponse.json(
        { error: 'Organization URL is already taken' },
        { status: 400 }
      )
    }

    // Check if organization domain is already taken (if provided)
    if (organizationSlug.includes('.')) {
      const existingDomain = await db.organization.findUnique({
        where: { domain: organizationSlug }
      })

      if (existingDomain) {
        return NextResponse.json(
          { error: 'Organization domain is already taken' },
          { status: 400 }
        )
      }
    }

    // Set plan limits
    const planLimits = {
      FREE: { maxUsers: 1, maxQuotes: 10, maxAiUsage: 20 },
      STARTER: { maxUsers: 5, maxQuotes: 200, maxAiUsage: 1000 },
      PROFESSIONAL: { maxUsers: 25, maxQuotes: 999999, maxAiUsage: 10000 }
    }

    const limits = planLimits[plan as keyof typeof planLimits] || planLimits.FREE

    // Create organization
    const organization = await db.organization.create({
      data: {
        name: organizationName,
        slug: organizationSlug,
        plan: plan,
        status: 'TRIAL',
        maxUsers: limits.maxUsers,
        maxQuotes: limits.maxQuotes,
        maxAiUsage: limits.maxAiUsage,
        // Store additional contact info
        phone: phone || null,
        address: address || null
      }
    })

    // Create user profile
    const user = await db.profile.create({
      data: {
        email,
        name,
        organizationId: organization.id,
        role: 'OWNER',
        // Store additional user info
        phone: phone || null
      }
    })

    return NextResponse.json({
      message: 'User created successfully',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        organizationId: user.organizationId,
        organizationName: organization.name,
        plan: organization.plan
      }
    })

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}