import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { combineMiddleware, requireAuth, rateLimit, requireRole } from '@/lib/middleware'
import { <PERSON>tLogger } from '@/lib/audit-logger'
import { z } from 'zod'

const auditQuerySchema = z.object({
  action: z.string().optional(),
  resource: z.string().optional(),
  userId: z.string().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  limit: z.string().transform(val => Math.min(parseInt(val) || 100, 1000)).optional()
})

export async function GET(request: NextRequest) {
  // Apply middleware - only admins and owners can view audit logs
  const middlewareResult = await combineMiddleware(
    rateLimit(50, 15 * 60 * 1000),
    requireAuth,
    requireRole(['OWNER', 'ADMIN'])
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validationResult = auditQuerySchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { action, resource, userId, startDate, endDate, limit } = validationResult.data

    const filters: any = {}
    if (action) filters.action = action
    if (resource) filters.resource = resource
    if (userId) filters.userId = userId
    if (startDate) filters.startDate = new Date(startDate)
    if (endDate) filters.endDate = new Date(endDate)
    if (limit) filters.limit = limit

    const auditLogs = await AuditLogger.getAuditLogs(organizationId, filters)

    // Log this audit log access
    await AuditLogger.logDataAccess(
      'READ',
      'AUDIT_LOGS',
      'audit-logs-query',
      session.user.id,
      organizationId,
      request
    )

    return NextResponse.json({
      data: auditLogs,
      total: auditLogs.length,
      filters: {
        action,
        resource,
        userId,
        startDate,
        endDate,
        limit
      }
    })

  } catch (error) {
    console.error('Error fetching audit logs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
