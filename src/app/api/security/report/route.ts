import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { combineMiddleware, requireAuth, rateLimit, requireRole } from '@/lib/middleware'
import { <PERSON>tLogger } from '@/lib/audit-logger'
import { db } from '@/lib/db'
import { z } from 'zod'

const reportQuerySchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  format: z.enum(['json', 'csv']).default('json')
})

export async function GET(request: NextRequest) {
  // Apply middleware - only owners can generate security reports
  const middlewareResult = await combineMiddleware(
    rateLimit(10, 60 * 60 * 1000), // Very limited - 10 reports per hour
    requireAuth,
    requireRole(['OWNER'])
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validationResult = reportQuerySchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { startDate, endDate, format } = validationResult.data

    // Generate comprehensive security report
    const report = await generateSecurityReport(
      organizationId,
      new Date(startDate),
      new Date(endDate)
    )

    // Log report generation
    await AuditLogger.logDataAccess(
      'READ',
      'SECURITY_REPORT',
      `report-${startDate}-${endDate}`,
      session.user.id,
      organizationId,
      request
    )

    if (format === 'csv') {
      const csv = convertReportToCSV(report)
      const filename = `security-report-${startDate.split('T')[0]}-${endDate.split('T')[0]}.csv`
      
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      })
    }

    return NextResponse.json(report)

  } catch (error) {
    console.error('Error generating security report:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function generateSecurityReport(
  organizationId: string,
  startDate: Date,
  endDate: Date
) {
  // Get audit report
  const auditReport = await AuditLogger.generateAuditReport(
    organizationId,
    startDate,
    endDate
  )

  // Get user activity statistics
  const userStats = await db.profile.findMany({
    where: { organizationId },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      lastLoginAt: true,
      createdAt: true,
      _count: {
        select: {
          customers: true,
          quotes: true,
          activities: true
        }
      }
    }
  })

  // Get login statistics
  const loginStats = await db.activity.groupBy({
    by: ['userId'],
    where: {
      organizationId,
      type: 'AUDIT',
      title: { contains: 'LOGIN' },
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    },
    _count: { userId: true }
  })

  // Get failed login attempts
  const failedLogins = auditReport.events.filter(
    event => event.action === 'LOGIN_FAILED'
  )

  // Get data access patterns
  const dataAccessStats = auditReport.events
    .filter(event => ['CREATE', 'READ', 'UPDATE', 'DELETE'].includes(event.action))
    .reduce((acc, event) => {
      const key = `${event.action}_${event.resource}`
      acc[key] = (acc[key] || 0) + 1
      return acc
    }, {} as Record<string, number>)

  // Get IP address statistics
  const ipStats = auditReport.events
    .filter(event => event.ipAddress)
    .reduce((acc, event) => {
      acc[event.ipAddress] = (acc[event.ipAddress] || 0) + 1
      return acc
    }, {} as Record<string, number>)

  // Get most active users
  const userActivity = userStats.map(user => {
    const loginCount = loginStats.find(stat => stat.userId === user.id)?._count.userId || 0
    const auditEvents = auditReport.events.filter(event => event.user?.email === user.email)
    
    return {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        lastLoginAt: user.lastLoginAt
      },
      statistics: {
        loginCount,
        auditEventCount: auditEvents.length,
        customersCreated: user._count.customers,
        quotesCreated: user._count.quotes,
        activitiesCreated: user._count.activities
      }
    }
  }).sort((a, b) => b.statistics.auditEventCount - a.statistics.auditEventCount)

  // Security recommendations
  const recommendations: string[] = []
  
  if (failedLogins.length > 10) {
    recommendations.push('High number of failed login attempts detected. Consider implementing account lockout policies.')
  }
  
  if (Object.keys(ipStats).length > 20) {
    recommendations.push('Many different IP addresses accessing the system. Consider implementing IP whitelisting for sensitive operations.')
  }
  
  const inactiveUsers = userStats.filter(user => 
    !user.lastLoginAt || 
    (new Date().getTime() - user.lastLoginAt.getTime()) > 30 * 24 * 60 * 60 * 1000
  )
  
  if (inactiveUsers.length > 0) {
    recommendations.push(`${inactiveUsers.length} users haven't logged in for over 30 days. Consider deactivating unused accounts.`)
  }

  return {
    reportMetadata: {
      organizationId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      generatedAt: new Date().toISOString(),
      totalEvents: auditReport.events.length
    },
    summary: {
      totalAuditEvents: auditReport.events.length,
      securityEvents: auditReport.securityEvents.length,
      failedActions: auditReport.failedActions.length,
      uniqueUsers: userStats.length,
      uniqueIPs: Object.keys(ipStats).length,
      failedLogins: failedLogins.length
    },
    auditSummary: auditReport.summary,
    userActivity,
    securityEvents: auditReport.securityEvents,
    failedActions: auditReport.failedActions,
    dataAccessPatterns: dataAccessStats,
    ipAddressActivity: Object.entries(ipStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([ip, count]) => ({ ip, count })),
    recommendations,
    inactiveUsers: inactiveUsers.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      lastLoginAt: user.lastLoginAt,
      daysSinceLastLogin: user.lastLoginAt 
        ? Math.floor((new Date().getTime() - user.lastLoginAt.getTime()) / (24 * 60 * 60 * 1000))
        : null
    }))
  }
}

function convertReportToCSV(report: any): string {
  const sections = [
    // Summary
    'SECURITY REPORT SUMMARY',
    `Generated At,${report.reportMetadata.generatedAt}`,
    `Period,${report.reportMetadata.startDate} to ${report.reportMetadata.endDate}`,
    `Total Audit Events,${report.summary.totalAuditEvents}`,
    `Security Events,${report.summary.securityEvents}`,
    `Failed Actions,${report.summary.failedActions}`,
    `Unique Users,${report.summary.uniqueUsers}`,
    `Unique IPs,${report.summary.uniqueIPs}`,
    `Failed Logins,${report.summary.failedLogins}`,
    '',
    
    // User Activity
    'USER ACTIVITY',
    'Name,Email,Role,Login Count,Audit Events,Last Login',
    ...report.userActivity.map((user: any) => 
      `${user.user.name},${user.user.email},${user.user.role},${user.statistics.loginCount},${user.statistics.auditEventCount},${user.user.lastLoginAt || 'Never'}`
    ),
    '',
    
    // IP Activity
    'IP ADDRESS ACTIVITY',
    'IP Address,Request Count',
    ...report.ipAddressActivity.map((ip: any) => `${ip.ip},${ip.count}`),
    '',
    
    // Recommendations
    'SECURITY RECOMMENDATIONS',
    ...report.recommendations.map((rec: string) => `"${rec}"`),
    ''
  ]
  
  return sections.join('\n')
}
