import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { createPropertySchema, paginationSchema } from '@/lib/validations'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)

  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    const { page, limit, search, sortBy, sortOrder } = paginationSchema.parse(queryParams)

    // Build where clause
    const where: any = { organizationId }
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
        { propertyType: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    // Filter by customer if specified
    const customerId = searchParams.get('customerId')
    if (customerId) {
      where.customerId = customerId
    }

    // Build order by clause
    const orderBy: any = { createdAt: sortOrder }
    if (sortBy && ['name', 'address', 'propertyType', 'sizeM2', 'createdAt'].includes(sortBy)) {
      orderBy = { [sortBy]: sortOrder }
    }

    // Get total count for pagination
    const total = await db.property.count({ where })

    // Get properties with pagination
    const properties = await db.property.findMany({
      where,
      include: {
        customer: {
          select: { id: true, name: true, company: true }
        },
        _count: {
          select: {
            quotes: true
          }
        }
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit
    })

    return NextResponse.json({
      data: properties,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching properties:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(50, 15 * 60 * 1000),
    requireAuth
  )(request)

  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate input
    const body = await request.json()
    const validationResult = createPropertySchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { name, address, description, propertyType, sizeM2, customerId } = validationResult.data

    // Verify customer exists and belongs to organization
    const customer = await db.customer.findFirst({
      where: {
        id: customerId,
        organizationId
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found or does not belong to your organization' },
        { status: 400 }
      )
    }

    const property = await db.property.create({
      data: {
        name,
        address,
        description,
        propertyType,
        sizeM2,
        customerId,
        organizationId
      },
      include: {
        customer: {
          select: { id: true, name: true, company: true }
        },
        _count: {
          select: {
            quotes: true
          }
        }
      }
    })

    return NextResponse.json(property, { status: 201 })

  } catch (error) {
    console.error('Error creating property:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}