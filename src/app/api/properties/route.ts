import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = session.user.organizationId

    const properties = await db.property.findMany({
      where: { organizationId },
      include: {
        customer: {
          select: { name: true, company: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(properties)

  } catch (error) {
    console.error('Error fetching properties:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = session.user.organizationId
    const { name, address, description, propertyType, sizeM2, customerId } = await request.json()

    if (!name || !address || !customerId) {
      return NextResponse.json(
        { error: 'Name, address, and customer are required' },
        { status: 400 }
      )
    }

    const property = await db.property.create({
      data: {
        name,
        address,
        description,
        propertyType,
        sizeM2,
        customerId,
        organizationId
      },
      include: {
        customer: {
          select: { name: true, company: true }
        }
      }
    })

    return NextResponse.json(property)

  } catch (error) {
    console.error('Error creating property:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}