import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { updatePropertySchema, idParamSchema } from '@/lib/validations'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'

interface RouteParams {
  params: { id: string }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid property ID' },
        { status: 400 }
      )
    }

    const property = await db.property.findFirst({
      where: {
        id: params.id,
        organizationId
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            company: true,
            email: true,
            phone: true
          }
        },
        quotes: {
          select: {
            id: true,
            number: true,
            title: true,
            status: true,
            totaalPrijs: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: {
            quotes: true
          }
        }
      }
    })

    if (!property) {
      return NextResponse.json(
        { error: 'Property not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(property)

  } catch (error) {
    console.error('Error fetching property:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(50, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid property ID' },
        { status: 400 }
      )
    }

    // Validate input
    const body = await request.json()
    const validationResult = updatePropertySchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    // Check if property exists and belongs to organization
    const existingProperty = await db.property.findFirst({
      where: {
        id: params.id,
        organizationId
      }
    })

    if (!existingProperty) {
      return NextResponse.json(
        { error: 'Property not found' },
        { status: 404 }
      )
    }

    const { name, address, description, propertyType, sizeM2 } = validationResult.data

    const updatedProperty = await db.property.update({
      where: { id: params.id },
      data: {
        ...(name && { name }),
        ...(address && { address }),
        ...(description !== undefined && { description }),
        ...(propertyType !== undefined && { propertyType }),
        ...(sizeM2 !== undefined && { sizeM2 })
      },
      include: {
        customer: {
          select: { id: true, name: true, company: true }
        },
        _count: {
          select: {
            quotes: true
          }
        }
      }
    })

    return NextResponse.json(updatedProperty)

  } catch (error) {
    console.error('Error updating property:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(25, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid property ID' },
        { status: 400 }
      )
    }

    // Check if property exists and belongs to organization
    const existingProperty = await db.property.findFirst({
      where: {
        id: params.id,
        organizationId
      },
      include: {
        _count: {
          select: {
            quotes: true
          }
        }
      }
    })

    if (!existingProperty) {
      return NextResponse.json(
        { error: 'Property not found' },
        { status: 404 }
      )
    }

    // Check if property has associated quotes
    if (existingProperty._count.quotes > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete property with associated quotes',
          details: {
            quotes: existingProperty._count.quotes
          }
        },
        { status: 400 }
      )
    }

    // Delete property
    await db.property.delete({
      where: { id: params.id }
    })

    return NextResponse.json(
      { message: 'Property deleted successfully' },
      { status: 200 }
    )

  } catch (error) {
    console.error('Error deleting property:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
