import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = session.user.organizationId

    // Get recent quotes
    const recentQuotes = await db.quote.findMany({
      where: { organizationId },
      include: {
        user: {
          select: { name: true }
        },
        customer: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    // Get recent customers
    const recentCustomers = await db.customer.findMany({
      where: { organizationId },
      include: {
        createdBy: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    // Get recent AI conversations
    const recentAiConversations = await db.aiConversation.findMany({
      where: { organizationId },
      include: {
        user: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 3
    })

    // Get recent activities (from activities table)
    const recentActivities = await db.activity.findMany({
      where: { organizationId },
      include: {
        user: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    })

    // Combine and format all activities
    const activities = [
      ...recentQuotes.map(quote => ({
        id: `quote-${quote.id}`,
        type: 'QUOTE' as const,
        title: `New Quote: ${quote.title}`,
        description: `Quote created for ${quote.customer?.name || 'Unknown Customer'}`,
        timestamp: quote.createdAt.toISOString(),
        user: quote.user?.name || 'Unknown User'
      })),
      ...recentCustomers.map(customer => ({
        id: `customer-${customer.id}`,
        type: 'CUSTOMER' as const,
        title: `New Customer: ${customer.name}`,
        description: `Customer added to system`,
        timestamp: customer.createdAt.toISOString(),
        user: customer.createdBy?.name || 'Unknown User'
      })),
      ...recentAiConversations.map(conversation => ({
        id: `ai-${conversation.id}`,
        type: 'AI' as const,
        title: `AI Conversation: ${conversation.title || 'Untitled'}`,
        description: `AI assistant interaction`,
        timestamp: conversation.createdAt.toISOString(),
        user: conversation.user?.name || 'Unknown User'
      })),
      ...recentActivities.map(activity => ({
        id: `activity-${activity.id}`,
        type: activity.type as any,
        title: activity.title,
        description: activity.description || '',
        timestamp: activity.createdAt.toISOString(),
        user: activity.user?.name || 'Unknown User'
      }))
    ]

    // Sort all activities by timestamp (most recent first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Take only the most recent 10 activities
    const recentActivitiesList = activities.slice(0, 10)

    // Format timestamps for display
    const formattedActivities = recentActivitiesList.map(activity => ({
      ...activity,
      timestamp: formatTimestamp(activity.timestamp)
    }))

    return NextResponse.json(formattedActivities)

  } catch (error) {
    console.error('Error fetching dashboard activities:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function formatTimestamp(timestamp: string): string {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes}m ago`
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`
  } else if (diffInHours < 48) {
    return 'Yesterday'
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }
}