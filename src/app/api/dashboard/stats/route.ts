import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { getDashboardStats } from '@/lib/analytics-utils'

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)

  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Get comprehensive dashboard statistics
    const stats = await getDashboardStats(organizationId)

    // Get AI usage information
    const now = new Date()
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    const [aiConversationsThisMonth, organization] = await Promise.all([
      db.aiConversation.count({
        where: {
          organizationId,
          createdAt: { gte: thisMonth }
        }
      }),
      db.organization.findUnique({
        where: { id: organizationId },
        select: { maxAiUsage: true, plan: true }
      })
    ])

    const maxAiUsage = organization?.maxAiUsage || 100
    const aiUsagePercentage = maxAiUsage > 0
      ? Math.round((aiConversationsThisMonth / maxAiUsage) * 100)
      : 0

    return NextResponse.json({
      // Legacy format for backward compatibility
      totalQuotes: stats.quotes.total,
      totalCustomers: stats.customers.total,
      monthlyRevenue: stats.revenue.thisMonth,
      aiUsage: Math.min(aiUsagePercentage, 100),
      quoteGrowth: stats.quotes.growth,
      customerGrowth: stats.customers.growth,
      revenueGrowth: stats.revenue.growth,

      // Enhanced statistics
      stats: {
        customers: stats.customers,
        quotes: stats.quotes,
        revenue: stats.revenue,
        activities: stats.activities,
        ai: {
          usage: aiConversationsThisMonth,
          limit: maxAiUsage,
          percentage: Math.min(aiUsagePercentage, 100),
          plan: organization?.plan || 'FREE'
        }
      }
    })

  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}