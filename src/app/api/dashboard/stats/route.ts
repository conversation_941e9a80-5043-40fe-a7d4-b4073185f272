import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const organizationId = session.user.organizationId

    // Get current date and last month date
    const now = new Date()
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    // Get total quotes
    const totalQuotes = await db.quote.count({
      where: { organizationId }
    })

    // Get quotes this month vs last month
    const quotesThisMonth = await db.quote.count({
      where: {
        organizationId,
        createdAt: { gte: thisMonth }
      }
    })

    const quotesLastMonth = await db.quote.count({
      where: {
        organizationId,
        createdAt: { gte: lastMonth, lt: thisMonth }
      }
    })

    const quoteGrowth = quotesLastMonth > 0 
      ? Math.round(((quotesThisMonth - quotesLastMonth) / quotesLastMonth) * 100)
      : quotesThisMonth > 0 ? 100 : 0

    // Get total customers
    const totalCustomers = await db.customer.count({
      where: { organizationId }
    })

    // Get customers this month vs last month
    const customersThisMonth = await db.customer.count({
      where: {
        organizationId,
        createdAt: { gte: thisMonth }
      }
    })

    const customersLastMonth = await db.customer.count({
      where: {
        organizationId,
        createdAt: { gte: lastMonth, lt: thisMonth }
      }
    })

    const customerGrowth = customersLastMonth > 0 
      ? Math.round(((customersThisMonth - customersLastMonth) / customersLastMonth) * 100)
      : customersThisMonth > 0 ? 100 : 0

    // Get monthly revenue (from accepted quotes)
    const acceptedQuotesThisMonth = await db.quote.findMany({
      where: {
        organizationId,
        status: 'ACCEPTED',
        createdAt: { gte: thisMonth }
      },
      select: { totaalPrijs: true }
    })

    const monthlyRevenue = acceptedQuotesThisMonth.reduce(
      (sum, quote) => sum + (quote.totaalPrijs || 0), 
      0
    )

    const acceptedQuotesLastMonth = await db.quote.findMany({
      where: {
        organizationId,
        status: 'ACCEPTED',
        createdAt: { gte: lastMonth, lt: thisMonth }
      },
      select: { totaalPrijs: true }
    })

    const revenueLastMonth = acceptedQuotesLastMonth.reduce(
      (sum, quote) => sum + (quote.totaalPrijs || 0), 
      0
    )

    const revenueGrowth = revenueLastMonth > 0 
      ? Math.round(((monthlyRevenue - revenueLastMonth) / revenueLastMonth) * 100)
      : monthlyRevenue > 0 ? 100 : 0

    // Get AI usage (from AI conversations)
    const aiConversationsThisMonth = await db.aiConversation.count({
      where: {
        organizationId,
        createdAt: { gte: thisMonth }
      }
    })

    const aiConversationsLastMonth = await db.aiConversation.count({
      where: {
        organizationId,
        createdAt: { gte: lastMonth, lt: thisMonth }
      }
    })

    // Get organization AI usage limit
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { maxAiUsage: true }
    })

    const maxAiUsage = organization?.maxAiUsage || 100
    const aiUsagePercentage = maxAiUsage > 0 
      ? Math.round((aiConversationsThisMonth / maxAiUsage) * 100)
      : 0

    const aiGrowth = aiConversationsLastMonth > 0 
      ? Math.round(((aiConversationsThisMonth - aiConversationsLastMonth) / aiConversationsLastMonth) * 100)
      : aiConversationsThisMonth > 0 ? 100 : 0

    return NextResponse.json({
      totalQuotes,
      totalCustomers,
      monthlyRevenue,
      aiUsage: Math.min(aiUsagePercentage, 100),
      quoteGrowth,
      customerGrowth,
      revenueGrowth,
      aiGrowth
    })

  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}