import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { getTopCustomers, getDateRanges } from '@/lib/analytics-utils'
import { db } from '@/lib/db'
import { z } from 'zod'

const reportQuerySchema = z.object({
  type: z.enum(['top-customers', 'quote-summary', 'revenue-summary', 'activity-summary']),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  range: z.enum(['today', 'yesterday', 'thisWeek', 'lastWeek', 'thisMonth', 'lastMonth', 'thisYear', 'lastYear']).optional(),
  limit: z.string().transform(val => parseInt(val) || 10).optional()
})

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(50, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validationResult = reportQuerySchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { type, startDate, endDate, range, limit } = validationResult.data

    // Determine date range
    let dateRange
    if (startDate && endDate) {
      dateRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    } else if (range) {
      const ranges = getDateRanges()
      dateRange = ranges[range]
    } else {
      // Default to this month
      const ranges = getDateRanges()
      dateRange = ranges.thisMonth
    }

    const filter = { dateRange }

    let reportData
    switch (type) {
      case 'top-customers':
        reportData = await getTopCustomersReport(organizationId, filter, limit || 10)
        break
      
      case 'quote-summary':
        reportData = await getQuoteSummaryReport(organizationId, filter)
        break
      
      case 'revenue-summary':
        reportData = await getRevenueSummaryReport(organizationId, filter)
        break
      
      case 'activity-summary':
        reportData = await getActivitySummaryReport(organizationId, filter)
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid report type' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      type,
      dateRange: {
        start: dateRange.start.toISOString(),
        end: dateRange.end.toISOString()
      },
      generatedAt: new Date().toISOString(),
      data: reportData
    })

  } catch (error) {
    console.error('Error generating report:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Top customers report
async function getTopCustomersReport(organizationId: string, filter: any, limit: number) {
  const topCustomers = await getTopCustomers(organizationId, limit, filter)
  
  const totalRevenue = topCustomers.reduce((sum, item) => sum + item.revenue, 0)
  const totalQuotes = topCustomers.reduce((sum, item) => sum + item.quoteCount, 0)

  return {
    summary: {
      totalCustomers: topCustomers.length,
      totalRevenue,
      totalQuotes,
      averageRevenuePerCustomer: topCustomers.length > 0 ? totalRevenue / topCustomers.length : 0
    },
    customers: topCustomers.map((item, index) => ({
      rank: index + 1,
      customer: item.customer,
      revenue: item.revenue,
      quoteCount: item.quoteCount,
      averageQuoteValue: item.quoteCount > 0 ? item.revenue / item.quoteCount : 0,
      revenuePercentage: totalRevenue > 0 ? (item.revenue / totalRevenue * 100).toFixed(1) : '0'
    }))
  }
}

// Quote summary report
async function getQuoteSummaryReport(organizationId: string, filter: any) {
  const where: any = { organizationId }
  if (filter.dateRange) {
    where.createdAt = {
      gte: filter.dateRange.start,
      lte: filter.dateRange.end
    }
  }

  const [
    totalQuotes,
    statusCounts,
    averageValue,
    totalValue
  ] = await Promise.all([
    db.quote.count({ where }),
    db.quote.groupBy({
      by: ['status'],
      where,
      _count: { status: true }
    }),
    db.quote.aggregate({
      where,
      _avg: { totaalPrijs: true }
    }),
    db.quote.aggregate({
      where,
      _sum: { totaalPrijs: true }
    })
  ])

  const statusSummary = statusCounts.reduce((acc, item) => {
    acc[item.status] = item._count.status
    return acc
  }, {} as Record<string, number>)

  const conversionRate = statusSummary.SENT > 0 
    ? ((statusSummary.ACCEPTED || 0) / statusSummary.SENT * 100).toFixed(1)
    : '0'

  return {
    summary: {
      totalQuotes,
      totalValue: totalValue._sum.totaalPrijs || 0,
      averageValue: averageValue._avg.totaalPrijs || 0,
      conversionRate: `${conversionRate}%`
    },
    statusBreakdown: {
      draft: statusSummary.DRAFT || 0,
      sent: statusSummary.SENT || 0,
      accepted: statusSummary.ACCEPTED || 0,
      rejected: statusSummary.REJECTED || 0,
      expired: statusSummary.EXPIRED || 0
    },
    percentages: {
      draft: totalQuotes > 0 ? ((statusSummary.DRAFT || 0) / totalQuotes * 100).toFixed(1) : '0',
      sent: totalQuotes > 0 ? ((statusSummary.SENT || 0) / totalQuotes * 100).toFixed(1) : '0',
      accepted: totalQuotes > 0 ? ((statusSummary.ACCEPTED || 0) / totalQuotes * 100).toFixed(1) : '0',
      rejected: totalQuotes > 0 ? ((statusSummary.REJECTED || 0) / totalQuotes * 100).toFixed(1) : '0',
      expired: totalQuotes > 0 ? ((statusSummary.EXPIRED || 0) / totalQuotes * 100).toFixed(1) : '0'
    }
  }
}

// Revenue summary report
async function getRevenueSummaryReport(organizationId: string, filter: any) {
  const where: any = { 
    organizationId,
    status: 'ACCEPTED' // Only count accepted quotes as revenue
  }
  
  if (filter.dateRange) {
    where.createdAt = {
      gte: filter.dateRange.start,
      lte: filter.dateRange.end
    }
  }

  const [
    totalRevenue,
    revenueByMonth,
    topQuotes
  ] = await Promise.all([
    db.quote.aggregate({
      where,
      _sum: { totaalPrijs: true },
      _count: { id: true },
      _avg: { totaalPrijs: true }
    }),
    db.quote.findMany({
      where,
      select: {
        totaalPrijs: true,
        createdAt: true
      }
    }),
    db.quote.findMany({
      where,
      select: {
        id: true,
        number: true,
        title: true,
        totaalPrijs: true,
        customer: {
          select: { name: true, company: true }
        }
      },
      orderBy: { totaalPrijs: 'desc' },
      take: 10
    })
  ])

  // Group revenue by month
  const monthlyRevenue = new Map<string, number>()
  revenueByMonth.forEach(quote => {
    const month = quote.createdAt.toISOString().substring(0, 7) // YYYY-MM
    monthlyRevenue.set(month, (monthlyRevenue.get(month) || 0) + quote.totaalPrijs)
  })

  return {
    summary: {
      totalRevenue: totalRevenue._sum.totaalPrijs || 0,
      totalQuotes: totalRevenue._count.id,
      averageQuoteValue: totalRevenue._avg.totaalPrijs || 0
    },
    monthlyBreakdown: Array.from(monthlyRevenue.entries()).map(([month, revenue]) => ({
      month,
      revenue
    })).sort((a, b) => a.month.localeCompare(b.month)),
    topQuotes: topQuotes.map((quote, index) => ({
      rank: index + 1,
      quote: {
        id: quote.id,
        number: quote.number,
        title: quote.title,
        value: quote.totaalPrijs
      },
      customer: quote.customer
    }))
  }
}

// Activity summary report
async function getActivitySummaryReport(organizationId: string, filter: any) {
  const where: any = { organizationId }
  if (filter.dateRange) {
    where.createdAt = {
      gte: filter.dateRange.start,
      lte: filter.dateRange.end
    }
  }

  const [
    totalActivities,
    activityTypes,
    completionStats,
    recentActivities
  ] = await Promise.all([
    db.activity.count({ where }),
    db.activity.groupBy({
      by: ['type'],
      where,
      _count: { type: true }
    }),
    db.activity.groupBy({
      by: ['completed'],
      where,
      _count: { completed: true }
    }),
    db.activity.findMany({
      where,
      include: {
        user: {
          select: { name: true }
        },
        customer: {
          select: { name: true, company: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 20
    })
  ])

  const typeBreakdown = activityTypes.reduce((acc, item) => {
    acc[item.type] = item._count.type
    return acc
  }, {} as Record<string, number>)

  const completedCount = completionStats.find(item => item.completed)?._count.completed || 0
  const pendingCount = completionStats.find(item => !item.completed)?._count.completed || 0
  const completionRate = totalActivities > 0 ? (completedCount / totalActivities * 100).toFixed(1) : '0'

  return {
    summary: {
      totalActivities,
      completed: completedCount,
      pending: pendingCount,
      completionRate: `${completionRate}%`
    },
    typeBreakdown,
    recentActivities: recentActivities.map(activity => ({
      id: activity.id,
      type: activity.type,
      title: activity.title,
      completed: activity.completed,
      createdAt: activity.createdAt,
      user: activity.user,
      customer: activity.customer
    }))
  }
}
