import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { 
  getQuoteStatusDistribution, 
  getRevenueOverTime, 
  getActivityTrends,
  getDateRanges 
} from '@/lib/analytics-utils'
import { z } from 'zod'

const chartQuerySchema = z.object({
  type: z.enum(['quote-status', 'revenue-over-time', 'activity-trends']),
  period: z.enum(['daily', 'weekly', 'monthly']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  range: z.enum(['today', 'yesterday', 'thisWeek', 'lastWeek', 'thisMonth', 'lastMonth', 'thisYear', 'lastYear']).optional()
})

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validationResult = chartQuerySchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { type, period, startDate, endDate, range } = validationResult.data

    // Determine date range
    let dateRange
    if (startDate && endDate) {
      dateRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    } else if (range) {
      const ranges = getDateRanges()
      dateRange = ranges[range]
    }

    const filter = dateRange ? { dateRange } : undefined

    let chartData
    switch (type) {
      case 'quote-status':
        chartData = await getQuoteStatusDistribution(organizationId, filter)
        break
      
      case 'revenue-over-time':
        chartData = await getRevenueOverTime(organizationId, period || 'monthly', filter)
        break
      
      case 'activity-trends':
        chartData = await getActivityTrends(organizationId, filter)
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid chart type' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      type,
      period: period || 'monthly',
      dateRange: dateRange ? {
        start: dateRange.start.toISOString(),
        end: dateRange.end.toISOString()
      } : null,
      data: chartData
    })

  } catch (error) {
    console.error('Error fetching chart data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
