import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { combineMiddleware, requireAuth, rateLimit } from '@/lib/middleware'
import { getDateRanges } from '@/lib/analytics-utils'
import { db } from '@/lib/db'
import { z } from 'zod'

const exportQuerySchema = z.object({
  type: z.enum(['customers', 'quotes', 'activities', 'revenue']),
  format: z.enum(['csv', 'json']).default('csv'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  range: z.enum(['today', 'yesterday', 'thisWeek', 'lastWeek', 'thisMonth', 'lastMonth', 'thisYear', 'lastYear']).optional()
})

export async function GET(request: NextRequest) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(10, 15 * 60 * 1000), // Limited export requests
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validationResult = exportQuerySchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { type, format, startDate, endDate, range } = validationResult.data

    // Determine date range
    let dateRange
    if (startDate && endDate) {
      dateRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    } else if (range) {
      const ranges = getDateRanges()
      dateRange = ranges[range]
    }

    let data: any[]
    let filename: string

    switch (type) {
      case 'customers':
        data = await exportCustomers(organizationId, dateRange)
        filename = `customers-export-${new Date().toISOString().split('T')[0]}`
        break
      
      case 'quotes':
        data = await exportQuotes(organizationId, dateRange)
        filename = `quotes-export-${new Date().toISOString().split('T')[0]}`
        break
      
      case 'activities':
        data = await exportActivities(organizationId, dateRange)
        filename = `activities-export-${new Date().toISOString().split('T')[0]}`
        break
      
      case 'revenue':
        data = await exportRevenue(organizationId, dateRange)
        filename = `revenue-export-${new Date().toISOString().split('T')[0]}`
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid export type' },
          { status: 400 }
        )
    }

    if (format === 'json') {
      return NextResponse.json(data, {
        headers: {
          'Content-Disposition': `attachment; filename="${filename}.json"`
        }
      })
    } else {
      // Convert to CSV
      const csv = convertToCSV(data)
      
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}.csv"`
        }
      })
    }

  } catch (error) {
    console.error('Error exporting data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Export customers data
async function exportCustomers(organizationId: string, dateRange?: any) {
  const where: any = { organizationId }
  if (dateRange) {
    where.createdAt = {
      gte: dateRange.start,
      lte: dateRange.end
    }
  }

  const customers = await db.customer.findMany({
    where,
    include: {
      createdBy: {
        select: { name: true }
      },
      _count: {
        select: {
          quotes: true,
          properties: true
        }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return customers.map(customer => ({
    id: customer.id,
    name: customer.name,
    email: customer.email || '',
    phone: customer.phone || '',
    company: customer.company || '',
    address: customer.address || '',
    notes: customer.notes || '',
    createdBy: customer.createdBy?.name || '',
    createdAt: customer.createdAt.toISOString(),
    totalQuotes: customer._count.quotes,
    totalProperties: customer._count.properties
  }))
}

// Export quotes data
async function exportQuotes(organizationId: string, dateRange?: any) {
  const where: any = { organizationId }
  if (dateRange) {
    where.createdAt = {
      gte: dateRange.start,
      lte: dateRange.end
    }
  }

  const quotes = await db.quote.findMany({
    where,
    include: {
      user: {
        select: { name: true }
      },
      customer: {
        select: { name: true, company: true }
      },
      property: {
        select: { name: true, address: true }
      },
      _count: {
        select: { items: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return quotes.map(quote => ({
    id: quote.id,
    number: quote.number,
    title: quote.title,
    description: quote.description || '',
    status: quote.status,
    customerName: quote.customer.name,
    customerCompany: quote.customer.company || '',
    propertyName: quote.property?.name || '',
    propertyAddress: quote.property?.address || '',
    basisPrijs: quote.basisPrijs,
    btwPercentage: quote.btwPercentage,
    btwBedrag: quote.btwBedrag,
    totaalPrijs: quote.totaalPrijs,
    itemCount: quote._count.items,
    aiGenerated: quote.aiGenerated,
    createdBy: quote.user.name,
    createdAt: quote.createdAt.toISOString(),
    updatedAt: quote.updatedAt.toISOString(),
    sentAt: quote.sentAt?.toISOString() || '',
    expiresAt: quote.expiresAt?.toISOString() || ''
  }))
}

// Export activities data
async function exportActivities(organizationId: string, dateRange?: any) {
  const where: any = { organizationId }
  if (dateRange) {
    where.createdAt = {
      gte: dateRange.start,
      lte: dateRange.end
    }
  }

  const activities = await db.activity.findMany({
    where,
    include: {
      user: {
        select: { name: true }
      },
      customer: {
        select: { name: true, company: true }
      },
      quote: {
        select: { number: true, title: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return activities.map(activity => ({
    id: activity.id,
    type: activity.type,
    title: activity.title,
    description: activity.description || '',
    completed: activity.completed,
    dueDate: activity.dueDate?.toISOString() || '',
    customerName: activity.customer?.name || '',
    customerCompany: activity.customer?.company || '',
    quoteNumber: activity.quote?.number || '',
    quoteTitle: activity.quote?.title || '',
    createdBy: activity.user.name,
    createdAt: activity.createdAt.toISOString()
  }))
}

// Export revenue data
async function exportRevenue(organizationId: string, dateRange?: any) {
  const where: any = { 
    organizationId,
    status: 'ACCEPTED' // Only accepted quotes count as revenue
  }
  
  if (dateRange) {
    where.createdAt = {
      gte: dateRange.start,
      lte: dateRange.end
    }
  }

  const quotes = await db.quote.findMany({
    where,
    include: {
      customer: {
        select: { name: true, company: true }
      },
      user: {
        select: { name: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return quotes.map(quote => ({
    quoteId: quote.id,
    quoteNumber: quote.number,
    quoteTitle: quote.title,
    customerName: quote.customer.name,
    customerCompany: quote.customer.company || '',
    revenue: quote.totaalPrijs,
    basisPrijs: quote.basisPrijs,
    btwBedrag: quote.btwBedrag,
    btwPercentage: quote.btwPercentage,
    createdBy: quote.user.name,
    acceptedAt: quote.createdAt.toISOString(),
    month: quote.createdAt.toISOString().substring(0, 7), // YYYY-MM
    year: quote.createdAt.getFullYear()
  }))
}

// Convert array of objects to CSV
function convertToCSV(data: any[]): string {
  if (data.length === 0) {
    return ''
  }

  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(',')
  
  const csvRows = data.map(row => {
    return headers.map(header => {
      const value = row[header]
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value
    }).join(',')
  })

  return [csvHeaders, ...csvRows].join('\n')
}
