import { GET, POST } from '../route'
import { getServerSession } from 'next-auth'
import { db } from '@/lib/db'

// Mock the dependencies
jest.mock('next-auth')
jest.mock('@/lib/db')

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>
const mockDb = db as jest.Mocked<typeof db>

describe('/api/customers', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/customers', () => {
    it('should return customers for authenticated user', async () => {
      // Arrange
      const mockSession = global.testUtils.createMockSession()
      const mockCustomers = [
        {
          id: 'customer-1',
          name: 'Test Customer',
          email: '<EMAIL>',
          phone: '+1234567890',
          company: 'Test Company',
          createdAt: new Date(),
          createdBy: { name: 'Test User' },
          _count: { quotes: 2, properties: 1 }
        }
      ]

      mockGetServerSession.mockResolvedValue(mockSession)
      mockDb.customer.count.mockResolvedValue(1)
      mockDb.customer.findMany.mockResolvedValue(mockCustomers)

      const request = global.testUtils.createMockRequest({
        url: 'http://localhost:3000/api/customers?page=1&limit=10'
      })

      // Act
      const response = await GET(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(200)
      expect(data.data).toEqual(mockCustomers)
      expect(data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        pages: 1
      })
      expect(mockDb.customer.findMany).toHaveBeenCalledWith({
        where: { organizationId: mockSession.user.organizationId },
        include: {
          createdBy: { select: { name: true } },
          _count: { select: { quotes: true, properties: true } }
        },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 10
      })
    })

    it('should return 401 for unauthenticated user', async () => {
      // Arrange
      mockGetServerSession.mockResolvedValue(null)
      const request = global.testUtils.createMockRequest()

      // Act
      const response = await GET(request)

      // Assert
      expect(response.status).toBe(401)
    })

    it('should handle search query', async () => {
      // Arrange
      const mockSession = global.testUtils.createMockSession()
      mockGetServerSession.mockResolvedValue(mockSession)
      mockDb.customer.count.mockResolvedValue(0)
      mockDb.customer.findMany.mockResolvedValue([])

      const request = global.testUtils.createMockRequest({
        url: 'http://localhost:3000/api/customers?search=test&page=1&limit=10'
      })

      // Act
      await GET(request)

      // Assert
      expect(mockDb.customer.findMany).toHaveBeenCalledWith({
        where: {
          organizationId: mockSession.user.organizationId,
          OR: [
            { name: { contains: 'test', mode: 'insensitive' } },
            { email: { contains: 'test', mode: 'insensitive' } },
            { company: { contains: 'test', mode: 'insensitive' } }
          ]
        },
        include: {
          createdBy: { select: { name: true } },
          _count: { select: { quotes: true, properties: true } }
        },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 10
      })
    })
  })

  describe('POST /api/customers', () => {
    it('should create a new customer', async () => {
      // Arrange
      const mockSession = global.testUtils.createMockSession()
      const customerData = {
        name: 'New Customer',
        email: '<EMAIL>',
        phone: '+1234567890',
        company: 'New Company'
      }
      const mockCreatedCustomer = {
        id: 'new-customer-id',
        ...customerData,
        organizationId: mockSession.user.organizationId,
        createdById: mockSession.user.id,
        createdAt: new Date(),
        createdBy: { name: mockSession.user.name },
        _count: { quotes: 0, properties: 0 }
      }

      mockGetServerSession.mockResolvedValue(mockSession)
      mockDb.customer.findFirst.mockResolvedValue(null) // No existing customer
      mockDb.customer.create.mockResolvedValue(mockCreatedCustomer)

      const request = global.testUtils.createMockRequest({
        method: 'POST',
        body: customerData
      })

      // Act
      const response = await POST(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(201)
      expect(data).toEqual(mockCreatedCustomer)
      expect(mockDb.customer.create).toHaveBeenCalledWith({
        data: {
          name: customerData.name,
          email: customerData.email,
          phone: customerData.phone,
          company: customerData.company,
          address: null,
          notes: undefined,
          organizationId: mockSession.user.organizationId,
          createdById: mockSession.user.id
        },
        include: {
          createdBy: { select: { name: true } },
          _count: { select: { quotes: true, properties: true } }
        }
      })
    })

    it('should return 400 for invalid data', async () => {
      // Arrange
      const mockSession = global.testUtils.createMockSession()
      mockGetServerSession.mockResolvedValue(mockSession)

      const request = global.testUtils.createMockRequest({
        method: 'POST',
        body: { name: '' } // Invalid: empty name
      })

      // Act
      const response = await POST(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation failed')
      expect(data.details).toBeDefined()
    })

    it('should return 400 for duplicate email', async () => {
      // Arrange
      const mockSession = global.testUtils.createMockSession()
      const customerData = {
        name: 'New Customer',
        email: '<EMAIL>'
      }

      mockGetServerSession.mockResolvedValue(mockSession)
      mockDb.customer.findFirst.mockResolvedValue({ id: 'existing-customer' }) // Existing customer

      const request = global.testUtils.createMockRequest({
        method: 'POST',
        body: customerData
      })

      // Act
      const response = await POST(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(data.error).toBe('Customer with this email already exists')
    })

    it('should return 401 for unauthenticated user', async () => {
      // Arrange
      mockGetServerSession.mockResolvedValue(null)
      const request = global.testUtils.createMockRequest({
        method: 'POST',
        body: { name: 'Test Customer' }
      })

      // Act
      const response = await POST(request)

      // Assert
      expect(response.status).toBe(401)
    })
  })

  describe('Error handling', () => {
    it('should handle database errors gracefully', async () => {
      // Arrange
      const mockSession = global.testUtils.createMockSession()
      mockGetServerSession.mockResolvedValue(mockSession)
      mockDb.customer.count.mockRejectedValue(new Error('Database error'))

      const request = global.testUtils.createMockRequest()

      // Act
      const response = await GET(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })
  })
})
