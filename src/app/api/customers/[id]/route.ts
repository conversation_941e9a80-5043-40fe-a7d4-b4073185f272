import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { updateCustomerSchema, idParamSchema } from '@/lib/validations'
import { combineMiddleware, requireAuth, rateLimit, requireOrganizationAccess, AuthenticatedRequest } from '@/lib/middleware'

interface RouteParams {
  params: { id: string }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(100, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid customer ID' },
        { status: 400 }
      )
    }

    const customer = await db.customer.findFirst({
      where: {
        id: params.id,
        organizationId
      },
      include: {
        createdBy: {
          select: { name: true }
        },
        properties: {
          select: {
            id: true,
            name: true,
            address: true,
            propertyType: true,
            sizeM2: true
          }
        },
        quotes: {
          select: {
            id: true,
            number: true,
            title: true,
            status: true,
            totaalPrijs: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        activities: {
          select: {
            id: true,
            type: true,
            title: true,
            completed: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: {
            quotes: true,
            properties: true,
            activities: true
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(customer)

  } catch (error) {
    console.error('Error fetching customer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(50, 15 * 60 * 1000),
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid customer ID' },
        { status: 400 }
      )
    }

    // Validate input
    const body = await request.json()
    const validationResult = updateCustomerSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    // Check if customer exists and belongs to organization
    const existingCustomer = await db.customer.findFirst({
      where: {
        id: params.id,
        organizationId
      }
    })

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    const { name, email, phone, company, address, notes } = validationResult.data

    // Check if email is being changed and if it conflicts with another customer
    if (email && email !== existingCustomer.email) {
      const emailConflict = await db.customer.findFirst({
        where: {
          organizationId,
          email,
          id: { not: params.id }
        }
      })

      if (emailConflict) {
        return NextResponse.json(
          { error: 'Another customer with this email already exists' },
          { status: 400 }
        )
      }
    }

    const updatedCustomer = await db.customer.update({
      where: { id: params.id },
      data: {
        ...(name && { name }),
        ...(email !== undefined && { email }),
        ...(phone !== undefined && { phone }),
        ...(company !== undefined && { company }),
        ...(address !== undefined && { address: address ? JSON.stringify(address) : null }),
        ...(notes !== undefined && { notes })
      },
      include: {
        createdBy: {
          select: { name: true }
        },
        _count: {
          select: {
            quotes: true,
            properties: true
          }
        }
      }
    })

    return NextResponse.json(updatedCustomer)

  } catch (error) {
    console.error('Error updating customer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  // Apply middleware
  const middlewareResult = await combineMiddleware(
    rateLimit(25, 15 * 60 * 1000), // Lower limit for DELETE operations
    requireAuth
  )(request)
  
  if (middlewareResult.status !== 200) {
    return middlewareResult
  }

  try {
    const session = await getServerSession(authOptions)
    const organizationId = session!.user!.organizationId

    // Validate ID parameter
    const idValidation = idParamSchema.safeParse({ id: params.id })
    if (!idValidation.success) {
      return NextResponse.json(
        { error: 'Invalid customer ID' },
        { status: 400 }
      )
    }

    // Check if customer exists and belongs to organization
    const existingCustomer = await db.customer.findFirst({
      where: {
        id: params.id,
        organizationId
      },
      include: {
        _count: {
          select: {
            quotes: true,
            properties: true
          }
        }
      }
    })

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    // Check if customer has associated data
    if (existingCustomer._count.quotes > 0 || existingCustomer._count.properties > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete customer with associated quotes or properties',
          details: {
            quotes: existingCustomer._count.quotes,
            properties: existingCustomer._count.properties
          }
        },
        { status: 400 }
      )
    }

    // Delete customer (cascade will handle related activities)
    await db.customer.delete({
      where: { id: params.id }
    })

    return NextResponse.json(
      { message: 'Customer deleted successfully' },
      { status: 200 }
    )

  } catch (error) {
    console.error('Error deleting customer:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
