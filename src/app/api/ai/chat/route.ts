import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import ZAI from 'z-ai-web-dev-sdk'

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  type?: 'text' | 'suggestion' | 'quote'
  metadata?: any
}

interface ChatRequest {
  message: string
  conversationHistory?: ChatMessage[]
  type?: 'text' | 'suggestion' | 'quote'
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: ChatRequest = await request.json()
    const { message, conversationHistory = [], type = 'text' } = body

    if (!message || !message.trim()) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 })
    }

    // Initialize ZAI SDK
    const zai = await ZAI.create()

    // Create system prompt based on context
    const systemPrompt = `Je bent een professionele AI assistent voor Quote.AI+CRM, een platform voor het genereren van offertes en klantrelatiebeheer in de bouwsector.

Je taken omvatten:
1. Offertes genereren met prijscalculaties
2. Projectadvies geven over bouwprojecten
3. Projectplanning maken
4. Actuele bouwprijzen en materiaalkosten delen
5. Helpen met materiaalkeuzes en technische specificaties

Belangrijke richtlijnen:
- Wees professioneel, vriendelijk en behulpzaam
- Geef gedetailleerde en praktische antwoorden
- Vermijd specifieke prijzen tenzij er duidelijke context is
- Wees transparant over schattingen en variabelen
- Geef veiligheidsadvies waar relevant
- Houd rekening met Nederlandse bouwvoorschriften

Gebruik Nederlands in je antwoorden tenzij specifiek gevraagd om Engels.

Huidige gebruiker: ${session.user.name}
Organisatie: ${session.user.organizationName || 'Onbekend'}
Rol: ${session.user.role || 'User'}`

    // Prepare conversation history for context
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt
      },
      ...conversationHistory.map((msg: ChatMessage) => ({
        role: msg.role,
        content: msg.content
      })),
      {
        role: 'user' as const,
        content: message
      }
    ]

    try {
      // Get AI response from ZAI
      const completion = await zai.chat.completions.create({
        messages,
        temperature: 0.7,
        max_tokens: 1000
      })

      const aiResponse = completion.choices[0]?.message?.content || 'Excuses, ik kon geen antwoord genereren.'
      
      // Analyze response type based on content
      let responseType: 'text' | 'suggestion' | 'quote' = 'text'
      let metadata: any = {}

      const lowerResponse = aiResponse.toLowerCase()
      
      if (lowerResponse.includes('offerte') || lowerResponse.includes('prijs') || lowerResponse.includes('€') || lowerResponse.includes('euro')) {
        responseType = 'quote'
        
        // Extract pricing information
        const priceMatches = aiResponse.match(/€[\d,.]+/g)
        if (priceMatches) {
          metadata.prices = priceMatches.map(price => parseFloat(price.replace('€', '').replace(',', '.')))
        }
      }
      
      if (lowerResponse.includes('suggestie') || lowerResponse.includes('advies') || lowerResponse.includes('tip')) {
        responseType = 'suggestion'
      }

      return NextResponse.json({
        response: aiResponse,
        type: responseType,
        metadata,
        timestamp: new Date()
      })

    } catch (aiError) {
      console.error('AI Service Error:', aiError)
      
      // Fallback response when AI service is unavailable
      const fallbackResponse = generateFallbackResponse(message, type)
      
      return NextResponse.json({
        response: fallbackResponse.content,
        type: fallbackResponse.type,
        metadata: fallbackResponse.metadata,
        timestamp: new Date(),
        fallback: true
      })
    }

  } catch (error) {
    console.error('Chat API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateFallbackResponse(message: string, type: string): { content: string; type: 'text' | 'suggestion' | 'quote'; metadata: any } {
  const lowerMessage = message.toLowerCase()
  
  if (lowerMessage.includes('offerte') || lowerMessage.includes('prijs') || lowerMessage.includes('kosten')) {
    return {
      content: `Ik kan je helpen met het maken van een offerte. Op basis van je verzoek heb ik een concept offerte gegenereerd:

**Project Offerte**
- Basisprijs: €2.500
- Materialen: €1.200
- Arbeid: €1.000
- BTW (21%): €525
- **Totaal: €3.025**

Dit is een schatting. Voor een exacte offerte heb ik meer details nodig over het project. Wil je dat ik deze offerte verder uitwerk?`,
      type: 'quote' as const,
      metadata: { basePrice: 2500, materials: 1200, labor: 1000, vat: 525, total: 3025 }
    }
  }

  if (lowerMessage.includes('bouw') || lowerMessage.includes('prijs') || lowerMessage.includes('huidig')) {
    return {
      content: `Hier zijn de huidige bouwprijzen voor veelvoorkomende werkzaamheden:

**Materialen (gemiddeld per m²):**
- Tegelwerk: €45-75
- Schilderwerk: €25-40
- Stucwerk: €20-35
- Vloeren: €40-120

**Arbeid (uurloon):**
- Tegelzetter: €35-45
- Schilder: €30-40
- Stukadoor: €25-35
- Algemeen bouwvakker: €20-30

Prijzen zijn exclusief BTW (21%) en kunnen variëren per regio en projectcomplexiteit.`,
      type: 'suggestion' as const,
      metadata: {}
    }
  }

  if (lowerMessage.includes('planning') || lowerMessage.includes('project')) {
    return {
      content: `Ik help je graag met projectplanning. Hier is een algemene planning voor een gemiddeld renovatieproject:

**Projectplanning (8-12 weken):**
1. **Week 1-2:** Voorbereiding en vergunningen
2. **Week 3-4:** Demontage en voorbereidend werk
3. **Week 5-8:** Hoofdwerkzaamheden
4. **Week 9-10:** Afwerking en detailwerk
5. **Week 11-12:** Oplevering en nazorg

**Belangrijke aandachtspunten:**
- Houd rekening met levertijden van materialen
- Plan buffer tijd voor onverwachte problemen
- Coördineer verschillende vakmensen goed

Wil je een specifiekere planning voor jouw project?`,
      type: 'suggestion' as const,
      metadata: {}
    }
  }

  return {
    content: `Ik begrijp je vraag over "${message}". Ik kan je helpen met:

- **Offerte generatie:** Automatisch offertes maken met prijscalculatie
- **Projectadvies:** Technisch advies en materiaalkeuzes
- **Planning:** Projectplanning en tijdschema's
- **Prijzen:** Actuele bouwprijzen en materiaalkosten

Kun je wat meer specifieker zijn over wat je nodig hebt?`,
    type: 'text' as const,
    metadata: {}
  }
}