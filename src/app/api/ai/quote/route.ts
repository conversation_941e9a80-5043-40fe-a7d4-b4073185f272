import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import ZAI from 'z-ai-web-dev-sdk'

interface QuoteGenerationRequest {
  projectDescription: string
  customerInfo?: {
    name?: string
    type?: string
    location?: string
    budget?: string
  }
  requirements?: {
    materials?: string[]
    services?: string[]
    timeline?: string
    specialRequests?: string
  }
  template?: string
}

interface QuoteItem {
  description: string
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number
  category: string
}

interface GeneratedQuote {
  title: string
  description: string
  items: QuoteItem[]
  subtotal: number
  vatRate: number
  vatAmount: number
  total: number
  terms: string[]
  notes: string[]
  validity: string
  estimatedDuration: string
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: QuoteGenerationRequest = await request.json()
    const { projectDescription, customerInfo = {}, requirements = {}, template = 'standard' } = body

    if (!projectDescription || !projectDescription.trim()) {
      return NextResponse.json({ error: 'Project description is required' }, { status: 400 })
    }

    // Initialize ZAI SDK
    const zai = await ZAI.create()

    // Create comprehensive prompt for quote generation
    const systemPrompt = `Je bent een professionele offerte generator voor Quote.AI+CRM. Je taak is om gedetailleerde, nauwkeurige offertes te genereren voor bouwprojecten.

Genereer offertes in het volgende JSON format:
{
  "title": "Project titel",
  "description": "Gedetailleerde projectbeschrijving",
  "items": [
    {
      "description": "Item beschrijving",
      "quantity": 1,
      "unit": "stuks",
      "unitPrice": 100.00,
      "totalPrice": 100.00,
      "category": "materialen|arbeid|apparatuur|overig"
    }
  ],
  "subtotal": 1000.00,
  "vatRate": 21,
  "vatAmount": 210.00,
  "total": 1210.00,
  "terms": ["Betalingsvoorwaarde 1", "Betalingsvoorwaarde 2"],
  "notes": ["Opmerking 1", "Opmerking 2"],
  "validity": "30 dagen",
  "estimatedDuration": "2-3 weken"
}

Richtlijnen:
- Gebruik realistische prijzen gebaseerd op Nederlandse markt
- Wees transparant over prijzen en breek alles down in duidelijke items
- Inclusief standaard BTW tarief van 21%
- Voeg relevante algemene voorwaarden toe
- Geef een realistische inschatting van de doorlooptijd
- Houd rekening met klanttype: ${customerInfo.type || 'particulier'}
- Locatie: ${customerInfo.location || 'Nederland'}
- Budgetindicatie: ${customerInfo.budget || 'niet gespecificeerd'}

Projectbeschrijving: ${projectDescription}

Specifieke eisen:
- Materialen: ${requirements.materials?.join(', ') || 'niet gespecificeerd'}
- Diensten: ${requirements.services?.join(', ') || 'niet gespecificeerd'}
- Tijdlijn: ${requirements.timeline || 'niet gespecificeerd'}
- Speciale verzoeken: ${requirements.specialRequests || 'geen'}

Genereer een complete, professionele offerte die klaar is om naar de klant te worden gestuurd.`

    try {
      // Get AI-generated quote from ZAI
      const completion = await zai.chat.completions.create({
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: `Genereer een offerte voor het volgende project: ${projectDescription}`
          }
        ],
        temperature: 0.4,
        max_tokens: 2000
      })

      const aiResponse = completion.choices[0]?.message?.content || '{}'
      
      // Parse the JSON response
      let generatedQuote: GeneratedQuote
      try {
        const parsed = JSON.parse(aiResponse)
        generatedQuote = parsed
      } catch (parseError) {
        console.error('Failed to parse AI quote:', parseError)
        // Fallback to default quote structure
        generatedQuote = generateFallbackQuote(projectDescription, customerInfo)
      }

      // Validate and enhance the quote
      const validatedQuote = validateAndEnhanceQuote(generatedQuote, customerInfo)

      return NextResponse.json({
        quote: validatedQuote,
        metadata: {
          generatedBy: 'ai',
          template: template,
          customerInfo,
          requirements,
          timestamp: new Date()
        }
      })

    } catch (aiError) {
      console.error('AI Service Error:', aiError)
      
      // Fallback quote generation
      const fallbackQuote = generateFallbackQuote(projectDescription, customerInfo)
      
      return NextResponse.json({
        quote: fallbackQuote,
        metadata: {
          generatedBy: 'fallback',
          template: template,
          customerInfo,
          requirements,
          timestamp: new Date()
        },
        fallback: true
      })
    }

  } catch (error) {
    console.error('Quote Generation API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function validateAndEnhanceQuote(quote: GeneratedQuote, customerInfo: any): GeneratedQuote {
  // Ensure required fields exist
  const validatedQuote: GeneratedQuote = {
    title: quote.title || 'Project Offerte',
    description: quote.description || 'Gedetailleerde projectbeschrijving',
    items: quote.items || [],
    subtotal: quote.subtotal || 0,
    vatRate: quote.vatRate || 21,
    vatAmount: quote.vatAmount || 0,
    total: quote.total || 0,
    terms: quote.terms || [
      'Offerte is 30 dagen geldig',
      'Prijzen zijn inclusief BTW',
      'Wijzigingen voorbehouden'
    ],
    notes: quote.notes || [
      'Neem contact op voor vragen',
      'Referenties beschikbaar op aanvraag'
    ],
    validity: quote.validity || '30 dagen',
    estimatedDuration: quote.estimatedDuration || '2-4 weken'
  }

  // Recalculate totals if needed
  if (validatedQuote.items.length > 0) {
    validatedQuote.subtotal = validatedQuote.items.reduce((sum, item) => sum + item.totalPrice, 0)
    validatedQuote.vatAmount = validatedQuote.subtotal * (validatedQuote.vatRate / 100)
    validatedQuote.total = validatedQuote.subtotal + validatedQuote.vatAmount
  }

  // Add customer-specific terms
  if (customerInfo.type === 'zakelijk') {
    validatedQuote.terms.push('Betalingstermijn: 14 dagen')
  } else {
    validatedQuote.terms.push('Aanbetaling: 50% bij start')
  }

  return validatedQuote
}

function generateFallbackQuote(projectDescription: string, customerInfo: any): GeneratedQuote {
  // Generate a basic fallback quote based on project description
  const isBathroom = projectDescription.toLowerCase().includes('badkamer')
  const isKitchen = projectDescription.toLowerCase().includes('keuken')
  const isConstruction = projectDescription.toLowerCase().includes('bouw') || projectDescription.toLowerCase().includes('verbouwing')

  let items: QuoteItem[] = []
  let estimatedDuration = '2-3 weken'

  if (isBathroom) {
    items = [
      { description: 'Demontage oude badkamer', quantity: 1, unit: 'project', unitPrice: 500, totalPrice: 500, category: 'arbeid' },
      { description: 'Tegelwerk wanden en vloer', quantity: 20, unit: 'm²', unitPrice: 45, totalPrice: 900, category: 'materialen' },
      { description: 'Sanitair installatie', quantity: 1, unit: 'set', unitPrice: 1200, totalPrice: 1200, category: 'materialen' },
      { description: 'Loodgieterswerk', quantity: 1, unit: 'project', unitPrice: 800, totalPrice: 800, category: 'arbeid' },
      { description: 'Stucwerk en afwerking', quantity: 1, unit: 'project', unitPrice: 600, totalPrice: 600, category: 'arbeid' }
    ]
    estimatedDuration = '1-2 weken'
  } else if (isKitchen) {
    items = [
      { description: 'Demontage oude keuken', quantity: 1, unit: 'project', unitPrice: 400, totalPrice: 400, category: 'arbeid' },
      { description: 'Keukeninstallatie', quantity: 1, unit: 'set', unitPrice: 2500, totalPrice: 2500, category: 'materialen' },
      { description: 'Electrische aansluitingen', quantity: 1, unit: 'project', unitPrice: 600, totalPrice: 600, category: 'arbeid' },
      { description: 'Afwerkwerkzaamheden', quantity: 1, unit: 'project', unitPrice: 400, totalPrice: 400, category: 'arbeid' }
    ]
    estimatedDuration = '1 week'
  } else {
    items = [
      { description: 'Voorbereidend werk', quantity: 1, unit: 'project', unitPrice: 800, totalPrice: 800, category: 'arbeid' },
      { description: 'Bouwmaterialen', quantity: 1, unit: 'project', unitPrice: 2000, totalPrice: 2000, category: 'materialen' },
      { description: 'Arbeidskosten', quantity: 40, unit: 'uur', unitPrice: 35, totalPrice: 1400, category: 'arbeid' },
      { description: 'Afwerking en oplevering', quantity: 1, unit: 'project', unitPrice: 600, totalPrice: 600, category: 'arbeid' }
    ]
  }

  const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0)
  const vatRate = 21
  const vatAmount = subtotal * (vatRate / 100)
  const total = subtotal + vatAmount

  return {
    title: `Offerte: ${projectDescription.substring(0, 50)}...`,
    description: projectDescription,
    items,
    subtotal,
    vatRate,
    vatAmount,
    total,
    terms: [
      'Offerte is 30 dagen geldig',
      'Prijzen zijn inclusief BTW',
      'Wijzigingen voorbehouden',
      customerInfo.type === 'zakelijk' ? 'Betalingstermijn: 14 dagen' : 'Aanbetaling: 50% bij start'
    ],
    notes: [
      'Neem contact op voor vragen',
      'Referenties beschikbaar op aanvraag',
      'Werkzaamheden worden uitgevoerd door gekwalificeerd personeel'
    ],
    validity: '30 dagen',
    estimatedDuration
  }
}