import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import ZAI from 'z-ai-web-dev-sdk'

interface SuggestionRequest {
  context?: {
    customerType?: string
    projectType?: string
    budget?: string
    location?: string
    urgency?: string
  }
  conversationHistory?: Array<{
    role: string
    content: string
  }>
  type?: 'quote' | 'materials' | 'planning' | 'pricing'
}

interface Suggestion {
  id: string
  title: string
  description: string
  category: string
  priority: 'high' | 'medium' | 'low'
  confidence: number
  action: {
    type: 'message' | 'quote' | 'template'
    data: any
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: SuggestionRequest = await request.json()
    const { context = {}, conversationHistory = [], type = 'quote' } = body

    // Initialize ZAI SDK
    const zai = await ZAI.create()

    // Create prompt for smart suggestions
    const systemPrompt = `Je bent een AI suggestie engine voor Quote.AI+CRM. Je taak is om intelligente suggesties te genereren voor bouwprofessionals op basis van context en gespreksgeschiedenis.

Genereer suggesties in de volgende JSON format:
{
  "suggestions": [
    {
      "id": "unique_id",
      "title": "Korte, duidelijke titel",
      "description": "Gedetailleerde beschrijving van de suggestie",
      "category": "quote|materials|planning|pricing",
      "priority": "high|medium|low",
      "confidence": 0.8,
      "action": {
        "type": "message|quote|template",
        "data": {}
      }
    }
  ]
}

Houd rekening met:
- Klanttype: ${context.customerType || 'onbekend'}
- Projecttype: ${context.projectType || 'algemeen'}
- Budget: ${context.budget || 'niet gespecificeerd'}
- Locatie: ${context.location || 'Nederland'}
- Urgentie: ${context.urgency || 'normaal'}

Geef maximaal 5 relevante suggesties. Sorteer op prioriteit en confidentie.`

    // Prepare conversation context
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt
      },
      ...conversationHistory.slice(-5).map((msg) => ({
        role: msg.role,
        content: msg.content
      })),
      {
        role: 'user' as const,
        content: `Genereer suggesties voor type: ${type}. Houd rekening met de huidige context.`
      }
    ]

    try {
      // Get AI suggestions from ZAI
      const completion = await zai.chat.completions.create({
        messages,
        temperature: 0.6,
        max_tokens: 1500
      })

      const aiResponse = completion.choices[0]?.message?.content || '[]'
      
      // Parse the JSON response
      let suggestions: Suggestion[] = []
      try {
        const parsed = JSON.parse(aiResponse)
        suggestions = parsed.suggestions || []
      } catch (parseError) {
        console.error('Failed to parse AI suggestions:', parseError)
        // Fallback to default suggestions
        suggestions = getDefaultSuggestions(type, context)
      }

      // Enhance suggestions with metadata
      const enhancedSuggestions = suggestions.map(suggestion => ({
        ...suggestion,
        timestamp: new Date(),
        userId: session.user.id,
        organizationId: session.user.organizationId
      }))

      return NextResponse.json({
        suggestions: enhancedSuggestions,
        timestamp: new Date(),
        context
      })

    } catch (aiError) {
      console.error('AI Service Error:', aiError)
      
      // Fallback to default suggestions
      const fallbackSuggestions = getDefaultSuggestions(type, context)
      
      return NextResponse.json({
        suggestions: fallbackSuggestions,
        timestamp: new Date(),
        context,
        fallback: true
      })
    }

  } catch (error) {
    console.error('Suggestions API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getDefaultSuggestions(type: string, context: any): Suggestion[] {
  const baseSuggestions: Suggestion[] = [
    {
      id: 'price_check',
      title: 'Controleer actuele prijzen',
      description: 'Vraag naar de meest actuele materiaalprijzen en arbeidskosten in jouw regio.',
      category: 'pricing',
      priority: 'high' as const,
      confidence: 0.9,
      action: {
        type: 'message',
        data: {
          message: 'Wat zijn de huidige prijzen voor bouwmaterialen en arbeid in deze regio?'
        }
      }
    },
    {
      id: 'template_quote',
      title: 'Gebruik offerte template',
      description: 'Start met een bewezen offerte template voor dit type project.',
      category: 'quote',
      priority: 'medium' as const,
      confidence: 0.8,
      action: {
        type: 'template',
        data: {
          templateId: 'standard_construction',
          projectType: context.projectType || 'general'
        }
      }
    },
    {
      id: 'material_advice',
      title: 'Materiaaladvies',
      description: 'Ontvang advies over de beste materialen voor dit project en budget.',
      category: 'materials',
      priority: 'medium' as const,
      confidence: 0.7,
      action: {
        type: 'message',
        data: {
          message: 'Welke materialen raad je aan voor dit project binnen het gestelde budget?'
        }
      }
    }
  ]

  // Add context-specific suggestions
  if (context.projectType?.toLowerCase().includes('badkamer')) {
    baseSuggestions.push({
      id: 'bathroom_specific',
      title: 'Badkamer specificaties',
      description: 'Vraag naar specifieke eisen voor de badkamerrenovatie.',
      category: 'planning',
      priority: 'high' as const,
      confidence: 0.85,
      action: {
        type: 'message',
        data: {
          message: 'Wat zijn de specifieke wensen voor de badkamer? (afmetingen, stijl, budget, gewenste voorzieningen)'
        }
      }
    })
  }

  if (context.urgency?.toLowerCase().includes('spoed')) {
    baseSuggestions.push({
      id: 'urgent_planning',
      title: 'Spoedplanning',
      description: 'Maak een versnelde planning voor dit urgente project.',
      category: 'planning',
      priority: 'high' as const,
      confidence: 0.9,
      action: {
        type: 'message',
        data: {
          message: 'Help me met een spoedplanning. Wanneer kunnen we beginnen en wat is de snelste opleverdatum?'
        }
      }
    })
  }

  return baseSuggestions.slice(0, 5) // Limit to 5 suggestions
}