'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Sparkles, Mail, Lock, Building, User, Eye, EyeOff, CheckCircle, Star, ArrowRight } from 'lucide-react'

const pricingPlans = [
  {
    id: 'FREE',
    name: 'FREE',
    price: '€0',
    period: 'per maand',
    description: 'Perfect voor starters',
    features: ['1 gebruiker', '10 offertes/maand', 'Basis AI', 'Email support'],
    popular: false,
    color: 'from-gray-500 to-gray-600'
  },
  {
    id: 'STARTER',
    name: 'STARTER',
    price: '€29',
    period: 'per maand',
    description: 'Ideal voor groeiende bedrijven',
    features: ['5 gebruikers', '200 offertes/maand', 'Advanced AI', 'WhatsApp integratie'],
    popular: true,
    color: 'from-blue-500 to-purple-500'
  },
  {
    id: 'PROFESSIONAL',
    name: 'PROFESSIONAL',
    price: '€99',
    period: 'per maand',
    description: 'Voor professionele bedrijven',
    features: ['25 gebruikers', 'Unlimited offertes', 'Premium AI', 'Custom branding'],
    popular: false,
    color: 'from-green-500 to-emerald-500'
  }
]

export default function RegisterPage() {
  const [step, setStep] = useState(1)
  const [selectedPlan, setSelectedPlan] = useState('FREE')
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    organizationName: '',
    organizationSlug: '',
    phone: '',
    address: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const router = useRouter()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleOrganizationNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value
    setFormData(prev => ({
      ...prev,
      organizationName: name,
      organizationSlug: generateSlug(name)
    }))
  }

  const validateStep1 = () => {
    if (!formData.name || !formData.email || !formData.password || !formData.confirmPassword) {
      setError('Please fill in all required fields')
      return false
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return false
    }
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return false
    }
    return true
  }

  const validateStep2 = () => {
    if (!formData.organizationName || !formData.organizationSlug) {
      setError('Organization name is required')
      return false
    }
    return true
  }

  const handleNextStep = () => {
    setError('')
    if (step === 1 && validateStep1()) {
      setStep(2)
    } else if (step === 2 && validateStep2()) {
      setStep(3)
    }
  }

  const handlePrevStep = () => {
    setError('')
    setStep(step - 1)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          plan: selectedPlan
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Account created successfully! Please sign in.')
        setTimeout(() => {
          router.push('/auth/login')
        }, 2000)
      } else {
        setError(data.error || 'An error occurred during registration')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-background dark flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Logo and Brand */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect">
            <Sparkles className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white text-glow mb-2">
            Join <span className="gradient-text">Quote.AI+CRM</span>
          </h1>
          <p className="text-white/60">Start your 14-day free trial</p>
        </div>

        {/* Progress Steps */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((stepNumber) => (
              <div key={stepNumber} className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                    step >= stepNumber
                      ? 'gradient-bg text-white'
                      : 'bg-white/10 text-white/40'
                  }`}
                >
                  {stepNumber}
                </div>
                <span className="ml-2 text-sm text-white/60">
                  {stepNumber === 1 ? 'Account' : stepNumber === 2 ? 'Organization' : 'Plan'}
                </span>
                {stepNumber < 3 && (
                  <div className="w-16 h-0.5 bg-white/20 mx-4"></div>
                )}
              </div>
            ))}
          </div>
        </div>

        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white text-center">
              {step === 1 && 'Create Your Account'}
              {step === 2 && 'Setup Your Organization'}
              {step === 3 && 'Choose Your Plan'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <Alert className="border-red-500/20 bg-red-500/10">
                  <AlertDescription className="text-red-400">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="border-green-500/20 bg-green-500/10">
                  <AlertDescription className="text-green-400">
                    {success}
                  </AlertDescription>
                </Alert>
              )}

              {/* Step 1: Account Information */}
              {step === 1 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-white/80">Full Name *</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        value={formData.name}
                        onChange={handleChange}
                        className="ai-chat-input pl-10"
                        placeholder="Enter your full name"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-white/80">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="ai-chat-input pl-10"
                        placeholder="Enter your email"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-white/80">Password *</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                      <Input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={handleChange}
                        className="ai-chat-input pl-10 pr-10"
                        placeholder="Create a password (min. 8 characters)"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60"
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-white/80">Confirm Password *</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        className="ai-chat-input pl-10 pr-10"
                        placeholder="Confirm your password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/60"
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      type="button"
                      onClick={handleNextStep}
                      className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
                    >
                      Next Step
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              )}

              {/* Step 2: Organization Information */}
              {step === 2 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="organizationName" className="text-white/80">Organization Name *</Label>
                    <div className="relative">
                      <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                      <Input
                        id="organizationName"
                        name="organizationName"
                        type="text"
                        value={formData.organizationName}
                        onChange={handleOrganizationNameChange}
                        className="ai-chat-input pl-10"
                        placeholder="Enter organization name"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="organizationSlug" className="text-white/80">Organization URL</Label>
                    <div className="relative">
                      <Input
                        id="organizationSlug"
                        name="organizationSlug"
                        type="text"
                        value={formData.organizationSlug}
                        readOnly
                        className="ai-chat-input pl-4 bg-white/5"
                        placeholder="your-organization"
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 text-sm">
                        .quote-ai-crm.nl
                      </span>
                    </div>
                    <p className="text-xs text-white/50">
                      This will be your unique organization URL. You can change it later.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-white/80">Phone Number</Label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleChange}
                        className="ai-chat-input"
                        placeholder="+31 6 1234 5678"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="address" className="text-white/80">Address</Label>
                      <Input
                        id="address"
                        name="address"
                        type="text"
                        value={formData.address}
                        onChange={handleChange}
                        className="ai-chat-input"
                        placeholder="Company address"
                      />
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handlePrevStep}
                      className="border-white/20 text-white hover:bg-white/10"
                    >
                      Previous
                    </Button>
                    <Button
                      type="button"
                      onClick={handleNextStep}
                      className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
                    >
                      Next Step
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              )}

              {/* Step 3: Plan Selection */}
              {step === 3 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <p className="text-white/60">Choose the perfect plan for your business. You can upgrade or downgrade at any time.</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {pricingPlans.map((plan) => (
                      <Card
                        key={plan.id}
                        className={`stat-card cursor-pointer transition-all duration-200 ${
                          selectedPlan === plan.id
                            ? 'ring-2 ring-blue-400/50 scale-105'
                            : 'hover:scale-102'
                        }`}
                        onClick={() => setSelectedPlan(plan.id)}
                      >
                        <CardContent className="p-4">
                          {plan.popular && (
                            <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white w-full mb-3 justify-center">
                              <Star className="h-3 w-3 mr-1" />
                              Most Popular
                            </Badge>
                          )}
                          
                          <div className="text-center mb-4">
                            <h3 className="text-lg font-bold text-white mb-1">{plan.name}</h3>
                            <div className="text-2xl font-bold text-white mb-1">{plan.price}</div>
                            <div className="text-xs text-white/60">{plan.period}</div>
                            <p className="text-xs text-white/70 mt-2">{plan.description}</p>
                          </div>

                          <ul className="space-y-2 mb-4">
                            {plan.features.map((feature, index) => (
                              <li key={index} className="flex items-center gap-2 text-xs">
                                <CheckCircle className="h-3 w-3 text-green-400 flex-shrink-0" />
                                <span className="text-white/80">{feature}</span>
                              </li>
                            ))}
                          </ul>

                          <div className={`w-full h-8 rounded-lg flex items-center justify-center text-sm font-medium ${
                            selectedPlan === plan.id
                              ? 'gradient-bg text-white'
                              : 'bg-white/10 text-white/60'
                          }`}>
                            {selectedPlan === plan.id && <CheckCircle className="h-4 w-4 mr-1" />}
                            {selectedPlan === plan.id ? 'Selected' : 'Select'}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  <div className="bg-blue-500/10 border border-blue-400/30 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-blue-400 mt-0.5" />
                      <div className="text-sm">
                        <div className="text-white font-medium mb-1">14-Day Free Trial</div>
                        <div className="text-white/70">
                          No credit card required. Upgrade to a paid plan when you're ready.
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handlePrevStep}
                      className="border-white/20 text-white hover:bg-white/10"
                    >
                      Previous
                    </Button>
                    <Button
                      type="submit"
                      className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
                      disabled={isLoading}
                    >
                      {isLoading ? 'Creating Account...' : 'Create Account'}
                    </Button>
                  </div>
                </div>
              )}

              <div className="text-center">
                <p className="text-white/60 text-sm">
                  Already have an account?{' '}
                  <a href="/auth/login" className="text-blue-300 hover:text-blue-200">
                    Sign in
                  </a>
                </p>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}