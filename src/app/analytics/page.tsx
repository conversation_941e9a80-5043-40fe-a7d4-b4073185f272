'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  DollarSign,
  Calendar,
  Download,
  RefreshCw,
  Target,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { DashboardLayout } from '@/components/dashboard/layout'

interface AnalyticsData {
  revenue: {
    total: number
    growth: number
    monthly: Array<{ month: string; amount: number }>
  }
  quotes: {
    total: number
    conversionRate: number
    averageValue: number
    statusBreakdown: Array<{ status: string; count: number; percentage: number }>
  }
  customers: {
    total: number
    newThisMonth: number
    retentionRate: number
    topCustomers: Array<{ name: string; value: number; quotes: number }>
  }
  performance: {
    averageResponseTime: number
    quoteGenerationTime: number
    customerSatisfaction: number
  }
}

export default function AnalyticsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'month' | 'quarter' | 'year'>('month')

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    loadAnalytics()
  }, [session, status, router, timeRange])

  const loadAnalytics = async () => {
    try {
      // Mock analytics data
      const mockAnalytics: AnalyticsData = {
        revenue: {
          total: 125000,
          growth: 23.5,
          monthly: [
            { month: 'Jan', amount: 15000 },
            { month: 'Feb', amount: 18000 },
            { month: 'Mar', amount: 22000 },
            { month: 'Apr', amount: 25000 },
            { month: 'May', amount: 28000 },
            { month: 'Jun', amount: 17000 }
          ]
        },
        quotes: {
          total: 156,
          conversionRate: 68.5,
          averageValue: 8200,
          statusBreakdown: [
            { status: 'ACCEPTED', count: 107, percentage: 68.5 },
            { status: 'SENT', count: 32, percentage: 20.5 },
            { status: 'DRAFT', count: 12, percentage: 7.7 },
            { status: 'REJECTED', count: 5, percentage: 3.2 }
          ]
        },
        customers: {
          total: 89,
          newThisMonth: 12,
          retentionRate: 92.3,
          topCustomers: [
            { name: 'Jan Jansen', value: 45000, quotes: 5 },
            { name: 'Marie de Vries', value: 32000, quotes: 3 },
            { name: 'Piet Bakker', value: 28000, quotes: 4 },
            { name: 'Elena Visser', value: 20000, quotes: 2 }
          ]
        },
        performance: {
          averageResponseTime: 2.4,
          quoteGenerationTime: 8.5,
          customerSatisfaction: 4.7
        }
      }

      setAnalytics(mockAnalytics)
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACCEPTED': return 'bg-green-500/20 text-green-400'
      case 'SENT': return 'bg-blue-500/20 text-blue-400'
      case 'DRAFT': return 'bg-yellow-500/20 text-yellow-400'
      case 'REJECTED': return 'bg-red-500/20 text-red-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <p className="text-white/60">Loading analytics...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!analytics) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <BarChart3 className="h-12 w-12 text-white/20 mx-auto mb-4" />
          <p className="text-white/60">No analytics data available</p>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">Analytics</h1>
            <p className="text-white/60 mt-1">
              Track your business performance and growth metrics
            </p>
          </div>
          <div className="flex gap-2">
            <div className="flex gap-1">
              {(['month', 'quarter', 'year'] as const).map((range) => (
                <Button
                  key={range}
                  variant={timeRange === range ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setTimeRange(range)}
                  className={timeRange === range ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                >
                  {range.charAt(0).toUpperCase() + range.slice(1)}
                </Button>
              ))}
            </div>
            <Button 
              variant="outline"
              className="text-white border-white/20 hover:bg-white/15"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button 
              variant="outline"
              className="text-white border-white/20 hover:bg-white/15"
              onClick={loadAnalytics}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Revenue
              </CardTitle>
              <DollarSign className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                €{analytics.revenue.total.toLocaleString()}
              </div>
              <div className="flex items-center space-x-1 text-xs">
                <TrendingUp className="h-3 w-3 text-green-400" />
                <span className="text-green-400">
                  +{analytics.revenue.growth}% from last period
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Conversion Rate
              </CardTitle>
              <Target className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {analytics.quotes.conversionRate}%
              </div>
              <p className="text-xs text-white/60">Quote to project rate</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Customers
              </CardTitle>
              <Users className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{analytics.customers.total}</div>
              <div className="flex items-center space-x-1 text-xs">
                <TrendingUp className="h-3 w-3 text-green-400" />
                <span className="text-green-400">
                  +{analytics.customers.newThisMonth} this month
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Avg. Quote Value
              </CardTitle>
              <FileText className="h-4 w-4 text-orange-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                €{analytics.quotes.averageValue.toLocaleString()}
              </div>
              <p className="text-xs text-white/60">Per accepted quote</p>
            </CardContent>
          </Card>
        </div>

        {/* Revenue Chart */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Revenue Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Simple bar chart representation */}
              <div className="flex items-end justify-between h-32 px-4">
                {analytics.revenue.monthly.map((month, index) => (
                  <div key={index} className="flex flex-col items-center flex-1">
                    <div 
                      className="w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded-t"
                      style={{ 
                        height: `${(month.amount / Math.max(...analytics.revenue.monthly.map(m => m.amount))) * 100}%`,
                        minHeight: '8px'
                      }}
                    />
                    <span className="text-xs text-white/60 mt-2">{month.month}</span>
                    <span className="text-xs text-white/80 mt-1">
                      €{(month.amount / 1000).toFixed(0)}k
                    </span>
                  </div>
                ))}
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-white/60">Monthly Revenue Trend</span>
                <span className="text-green-400 font-medium">
                  +{analytics.revenue.growth}% growth
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quote Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-white">Quote Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{analytics.quotes.total}</div>
                    <p className="text-sm text-white/60">Total Quotes</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{analytics.quotes.conversionRate}%</div>
                    <p className="text-sm text-white/60">Success Rate</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-white/80">Status Breakdown</h4>
                  {analytics.quotes.statusBreakdown.map((status) => (
                    <div key={status.status} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(status.status)}>
                            {status.status}
                          </Badge>
                          <span className="text-sm text-white/60">
                            {status.count} quotes
                          </span>
                        </div>
                        <span className="text-sm text-white font-medium">
                          {status.percentage}%
                        </span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            status.status === 'ACCEPTED' ? 'bg-green-500' :
                            status.status === 'SENT' ? 'bg-blue-500' :
                            status.status === 'DRAFT' ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}
                          style={{ width: `${status.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-white">Customer Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{analytics.customers.total}</div>
                    <p className="text-sm text-white/60">Total Customers</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{analytics.customers.retentionRate}%</div>
                    <p className="text-sm text-white/60">Retention Rate</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-white/80">Top Customers</h4>
                  {analytics.customers.topCustomers.map((customer, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded bg-white/5">
                      <div>
                        <p className="text-sm font-medium text-white">{customer.name}</p>
                        <p className="text-xs text-white/60">{customer.quotes} quotes</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-green-400">
                          €{customer.value.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="flex items-center justify-center w-16 h-16 mx-auto mb-3 rounded-full bg-blue-500/20">
                  <Clock className="h-8 w-8 text-blue-400" />
                </div>
                <div className="text-2xl font-bold text-white">
                  {analytics.performance.averageResponseTime}h
                </div>
                <p className="text-sm text-white/60">Avg. Response Time</p>
                <p className="text-xs text-green-400 mt-1">↑ 15% faster</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center w-16 h-16 mx-auto mb-3 rounded-full bg-purple-500/20">
                  <FileText className="h-8 w-8 text-purple-400" />
                </div>
                <div className="text-2xl font-bold text-white">
                  {analytics.performance.quoteGenerationTime}min
                </div>
                <p className="text-sm text-white/60">Quote Generation</p>
                <p className="text-xs text-green-400 mt-1">↑ 25% faster</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center w-16 h-16 mx-auto mb-3 rounded-full bg-green-500/20">
                  <CheckCircle className="h-8 w-8 text-green-400" />
                </div>
                <div className="text-2xl font-bold text-white">
                  {analytics.performance.customerSatisfaction}/5
                </div>
                <p className="text-sm text-white/60">Customer Satisfaction</p>
                <p className="text-xs text-green-400 mt-1">↑ 0.3 points</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Insights */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Key Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-5 w-5 text-green-400" />
                  <span className="text-green-400 font-medium">Revenue Growth</span>
                </div>
                <p className="text-sm text-white/80">
                  Your revenue has grown by {analytics.revenue.growth}% compared to the last period. 
                  The increase is driven by higher conversion rates and larger average deal sizes.
                </p>
              </div>
              
              <div className="p-4 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-5 w-5 text-blue-400" />
                  <span className="text-blue-400 font-medium">Conversion Opportunity</span>
                </div>
                <p className="text-sm text-white/80">
                  With {analytics.quotes.statusBreakdown.find(s => s.status === 'SENT')?.count} quotes pending, 
                  there's potential to increase your conversion rate by following up strategically.
                </p>
              </div>
              
              <div className="p-4 rounded-lg bg-purple-500/10 border border-purple-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="h-5 w-5 text-purple-400" />
                  <span className="text-purple-400 font-medium">Customer Retention</span>
                </div>
                <p className="text-sm text-white/80">
                  Your {analytics.customers.retentionRate}% retention rate is excellent. 
                  Focus on customer satisfaction to maintain this high level of loyalty.
                </p>
              </div>
              
              <div className="p-4 rounded-lg bg-orange-500/10 border border-orange-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <Activity className="h-5 w-5 text-orange-400" />
                  <span className="text-orange-400 font-medium">Efficiency Gains</span>
                </div>
                <p className="text-sm text-white/80">
                  AI-powered quote generation has reduced creation time by 25%, 
                  allowing you to serve more customers efficiently.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}