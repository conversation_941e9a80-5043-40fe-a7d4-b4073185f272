'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Plus, 
  Search, 
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Calendar,
  Euro,
  User,
  Building,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'

interface Quote {
  id: string
  number: string
  title: string
  description?: string
  status: 'DRAFT' | 'SENT' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED'
  basisPrijs: number
  btwPercentage: number
  btwBedrag: number
  totaalPrijs: number
  createdAt: string
  updatedAt: string
  sentAt?: string
  expiresAt?: string
  user: {
    name: string
  }
  customer: {
    name: string
    company?: string
  }
  property?: {
    name: string
    address: string
  }
  items: Array<{
    id: string
    beschrijving: string
    aantal: number
    eenheid: string
    eenheidPrijs: number
    totaalPrijs: number
  }>
}

interface FilterState {
  search: string
  status: string
  customer: string
  dateFrom: string
  dateTo: string
}

export default function QuotesPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [quotes, setQuotes] = useState<Quote[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: '',
    customer: '',
    dateFrom: '',
    dateTo: ''
  })
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    loadQuotes()
  }, [session, status, router])

  const loadQuotes = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/quotes')
      if (response.ok) {
        const data = await response.json()
        setQuotes(data)
      }
    } catch (error) {
      console.error('Error loading quotes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = !filters.search || 
      quote.title.toLowerCase().includes(filters.search.toLowerCase()) ||
      quote.number.toLowerCase().includes(filters.search.toLowerCase()) ||
      quote.customer.name.toLowerCase().includes(filters.search.toLowerCase())

    const matchesStatus = !filters.status || quote.status === filters.status

    const matchesCustomer = !filters.customer || 
      quote.customer.name.toLowerCase().includes(filters.customer.toLowerCase())

    const matchesDateFrom = !filters.dateFrom || 
      new Date(quote.createdAt) >= new Date(filters.dateFrom)

    const matchesDateTo = !filters.dateTo || 
      new Date(quote.createdAt) <= new Date(filters.dateTo)

    return matchesSearch && matchesStatus && matchesCustomer && matchesDateFrom && matchesDateTo
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-500/20 text-gray-400'
      case 'SENT': return 'bg-blue-500/20 text-blue-400'
      case 'ACCEPTED': return 'bg-green-500/20 text-green-400'
      case 'REJECTED': return 'bg-red-500/20 text-red-400'
      case 'EXPIRED': return 'bg-orange-500/20 text-orange-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const handleQuoteAction = (action: string, quoteId: string) => {
    switch (action) {
      case 'view':
        router.push(`/quotes/${quoteId}`)
        break
      case 'edit':
        router.push(`/quotes/${quoteId}/edit`)
        break
      case 'delete':
        // Handle delete (would need confirmation)
        break
      case 'download':
        // Handle PDF download
        break
      default:
        break
    }
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      customer: '',
      dateFrom: '',
      dateTo: ''
    })
  }

  const totalValue = filteredQuotes.reduce((sum, quote) => sum + quote.totaalPrijs, 0)
  const acceptedValue = filteredQuotes
    .filter(q => q.status === 'ACCEPTED')
    .reduce((sum, quote) => sum + quote.totaalPrijs, 0)

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
            <FileText className="h-8 w-8 text-white" />
          </div>
          <p className="text-white/60">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background dark p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">Quotes</h1>
            <p className="text-white/60 mt-1">
              Manage and track all your quotes
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="border-white/20 text-white hover:bg-white/10"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
            <Button
              onClick={() => router.push('/quotes/new')}
              className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Quote
            </Button>
          </div>
        </div>

        {/* Stats Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="stat-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Total Quotes</p>
                  <p className="text-2xl font-bold text-white">{filteredQuotes.length}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Total Value</p>
                  <p className="text-2xl font-bold text-white">€{totalValue.toLocaleString()}</p>
                </div>
                <Euro className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Accepted Value</p>
                  <p className="text-2xl font-bold text-white">€{acceptedValue.toLocaleString()}</p>
                </div>
                <ArrowUpRight className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        {showFilters && (
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-lg font-bold text-white flex items-center justify-between">
                <span>Filter Quotes</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="border-white/20 text-white hover:bg-white/10"
                >
                  Clear Filters
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="space-y-2">
                  <label className="text-white/80 text-sm">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                    <Input
                      value={filters.search}
                      onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                      className="ai-chat-input pl-10"
                      placeholder="Search quotes..."
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-white/80 text-sm">Status</label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="ai-chat-input w-full"
                  >
                    <option value="">All Status</option>
                    <option value="DRAFT">Draft</option>
                    <option value="SENT">Sent</option>
                    <option value="ACCEPTED">Accepted</option>
                    <option value="REJECTED">Rejected</option>
                    <option value="EXPIRED">Expired</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-white/80 text-sm">Customer</label>
                  <Input
                    value={filters.customer}
                    onChange={(e) => setFilters(prev => ({ ...prev, customer: e.target.value }))}
                    className="ai-chat-input"
                    placeholder="Filter by customer..."
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-white/80 text-sm">Date From</label>
                  <Input
                    type="date"
                    value={filters.dateFrom}
                    onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-white/80 text-sm">Date To</label>
                  <Input
                    type="date"
                    value={filters.dateTo}
                    onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                    className="ai-chat-input"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quotes List */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">
              Quotes ({filteredQuotes.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="p-4 rounded-lg bg-white/5 animate-pulse">
                    <div className="h-4 bg-white/20 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-white/10 rounded w-full mb-2"></div>
                    <div className="h-3 bg-white/10 rounded w-5/6"></div>
                  </div>
                ))}
              </div>
            ) : filteredQuotes.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-white/20 mx-auto mb-4" />
                <h3 className="text-white font-medium mb-2">No quotes found</h3>
                <p className="text-white/60 mb-4">
                  {quotes.length === 0 
                    ? "Get started by creating your first quote" 
                    : "Try adjusting your filters to see more results"
                  }
                </p>
                {quotes.length === 0 && (
                  <Button
                    onClick={() => router.push('/quotes/new')}
                    className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Quote
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredQuotes.map((quote) => (
                  <div
                    key={quote.id}
                    className="p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-200 border border-white/10"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-white font-medium">{quote.title}</h3>
                          <Badge variant="secondary" className={getStatusColor(quote.status)}>
                            {quote.status}
                          </Badge>
                          <span className="text-white/40 text-sm">#{quote.number}</span>
                        </div>
                        
                        <p className="text-white/60 text-sm mb-3">{quote.description}</p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4 text-white/40" />
                            <span className="text-white/60">Customer:</span>
                            <span className="text-white">
                              {quote.customer.name}
                              {quote.customer.company && ` (${quote.customer.company})`}
                            </span>
                          </div>
                          
                          {quote.property && (
                            <div className="flex items-center space-x-2">
                              <Building className="h-4 w-4 text-white/40" />
                              <span className="text-white/60">Property:</span>
                              <span className="text-white">{quote.property.name}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-white/40" />
                            <span className="text-white/60">Created:</span>
                            <span className="text-white">{formatDate(quote.createdAt)}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between mt-3 pt-3 border-t border-white/10">
                          <div className="flex items-center space-x-4 text-sm">
                            <span className="text-white/60">Base: €{quote.basisPrijs.toFixed(2)}</span>
                            <span className="text-white/60">VAT: €{quote.btwBedrag.toFixed(2)}</span>
                            <span className="text-white font-medium">Total: €{quote.totaalPrijs.toFixed(2)}</span>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleQuoteAction('view', quote.id)}
                              className="border-white/20 text-white hover:bg-white/10"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleQuoteAction('edit', quote.id)}
                              className="border-white/20 text-white hover:bg-white/10"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleQuoteAction('download', quote.id)}
                              className="border-white/20 text-white hover:bg-white/10"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}