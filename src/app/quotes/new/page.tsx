'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Save, 
  X, 
  Plus, 
  Trash2, 
  Calculator,
  Sparkles,
  ArrowLeft
} from 'lucide-react'

interface QuoteItem {
  id: string
  beschrijving: string
  aantal: number
  eenheid: string
  eenheidPrijs: number
  totaalPrijs: number
  volgorde: number
}

interface Customer {
  id: string
  name: string
  company?: string
}

interface Property {
  id: string
  name: string
  address: string
}

export default function NewQuotePage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    customerId: '',
    propertyId: '',
    status: 'DRAFT'
  })

  const [quoteItems, setQuoteItems] = useState<QuoteItem[]>([
    {
      id: '1',
      beschrijving: '',
      aantal: 1,
      eenheid: 'stuk',
      eenheidPrijs: 0,
      totaalPrijs: 0,
      volgorde: 1
    }
  ])

  const [customers, setCustomers] = useState<Customer[]>([])
  const [properties, setProperties] = useState<Property[]>([])
  const [pricing, setPricing] = useState({
    basisPrijs: 0,
    btwPercentage: 21,
    btwBedrag: 0,
    totaalPrijs: 0
  })

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    loadCustomers()
    loadProperties()
  }, [session, status, router])

  useEffect(() => {
    calculatePricing()
  }, [quoteItems])

  const loadCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      if (response.ok) {
        const data = await response.json()
        setCustomers(data)
      }
    } catch (error) {
      console.error('Error loading customers:', error)
    }
  }

  const loadProperties = async () => {
    try {
      const response = await fetch('/api/properties')
      if (response.ok) {
        const data = await response.json()
        setProperties(data)
      }
    } catch (error) {
      console.error('Error loading properties:', error)
    }
  }

  const calculatePricing = () => {
    const basisPrijs = quoteItems.reduce((sum, item) => sum + item.totaalPrijs, 0)
    const btwBedrag = basisPrijs * (pricing.btwPercentage / 100)
    const totaalPrijs = basisPrijs + btwBedrag

    setPricing(prev => ({
      ...prev,
      basisPrijs,
      btwBedrag,
      totaalPrijs
    }))
  }

  const addQuoteItem = () => {
    const newItem: QuoteItem = {
      id: Date.now().toString(),
      beschrijving: '',
      aantal: 1,
      eenheid: 'stuk',
      eenheidPrijs: 0,
      totaalPrijs: 0,
      volgorde: quoteItems.length + 1
    }
    setQuoteItems([...quoteItems, newItem])
  }

  const removeQuoteItem = (id: string) => {
    if (quoteItems.length === 1) return
    setQuoteItems(quoteItems.filter(item => item.id !== id))
  }

  const updateQuoteItem = (id: string, field: keyof QuoteItem, value: any) => {
    setQuoteItems(quoteItems.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }
        
        // Recalculate total price if quantity or unit price changes
        if (field === 'aantal' || field === 'eenheidPrijs') {
          updatedItem.totaalPrijs = updatedItem.aantal * updatedItem.eenheidPrijs
        }
        
        return updatedItem
      }
      return item
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          ...pricing,
          items: quoteItems
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Quote created successfully!')
        setTimeout(() => {
          router.push('/quotes')
        }, 2000)
      } else {
        setError(data.error || 'An error occurred while creating the quote')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const generateQuoteNumber = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `QUOTE-${year}${month}-${random}`
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
            <FileText className="h-8 w-8 text-white" />
          </div>
          <p className="text-white/60">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background dark p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard')}
              className="border-white/20 text-white hover:bg-white/10"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-white text-glow">Create New Quote</h1>
              <p className="text-white/60 mt-1">
                Generate professional quotes for your customers
              </p>
            </div>
          </div>
          <Badge variant="secondary" className="gradient-bg text-white">
            DRAFT
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Quote Information */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Quote Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {error && (
                  <Alert className="border-red-500/20 bg-red-500/10">
                    <AlertDescription className="text-red-400">
                      {error}
                    </AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="border-green-500/20 bg-green-500/10">
                    <AlertDescription className="text-green-400">
                      {success}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title" className="text-white/80">Quote Title *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      className="ai-chat-input"
                      placeholder="Enter quote title"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status" className="text-white/80">Status</Label>
                    <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                      <SelectTrigger className="ai-chat-input">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DRAFT">Draft</SelectItem>
                        <SelectItem value="SENT">Sent</SelectItem>
                        <SelectItem value="ACCEPTED">Accepted</SelectItem>
                        <SelectItem value="REJECTED">Rejected</SelectItem>
                        <SelectItem value="EXPIRED">Expired</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="text-white/80">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="ai-chat-input min-h-[100px]"
                    placeholder="Enter quote description"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="customer" className="text-white/80">Customer *</Label>
                    <Select value={formData.customerId} onValueChange={(value) => setFormData(prev => ({ ...prev, customerId: value }))}>
                      <SelectTrigger className="ai-chat-input">
                        <SelectValue placeholder="Select customer" />
                      </SelectTrigger>
                      <SelectContent>
                        {customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id}>
                            {customer.name} {customer.company && `(${customer.company})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="property" className="text-white/80">Property</Label>
                    <Select value={formData.propertyId} onValueChange={(value) => setFormData(prev => ({ ...prev, propertyId: value }))}>
                      <SelectTrigger className="ai-chat-input">
                        <SelectValue placeholder="Select property" />
                      </SelectTrigger>
                      <SelectContent>
                        {properties.map((property) => (
                          <SelectItem key={property.id} value={property.id}>
                            {property.name} - {property.address}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quote Items */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white flex items-center justify-between">
                  <div className="flex items-center">
                    <Calculator className="h-5 w-5 mr-2" />
                    Quote Items
                  </div>
                  <Button
                    type="button"
                    onClick={addQuoteItem}
                    variant="outline"
                    size="sm"
                    className="border-white/20 text-white hover:bg-white/10"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {quoteItems.map((item, index) => (
                  <div key={item.id} className="p-4 rounded-lg bg-white/5 space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="text-white font-medium">Item {index + 1}</h4>
                      {quoteItems.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeQuoteItem(item.id)}
                          variant="outline"
                          size="sm"
                          className="border-red-500/20 text-red-400 hover:bg-red-500/10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                      <div className="md:col-span-2">
                        <Label className="text-white/80 text-sm">Description</Label>
                        <Input
                          value={item.beschrijving}
                          onChange={(e) => updateQuoteItem(item.id, 'beschrijving', e.target.value)}
                          className="ai-chat-input text-sm"
                          placeholder="Item description"
                        />
                      </div>
                      
                      <div>
                        <Label className="text-white/80 text-sm">Quantity</Label>
                        <Input
                          type="number"
                          value={item.aantal}
                          onChange={(e) => updateQuoteItem(item.id, 'aantal', parseInt(e.target.value) || 0)}
                          className="ai-chat-input text-sm"
                          min="1"
                        />
                      </div>
                      
                      <div>
                        <Label className="text-white/80 text-sm">Unit Price (€)</Label>
                        <Input
                          type="number"
                          value={item.eenheidPrijs}
                          onChange={(e) => updateQuoteItem(item.id, 'eenheidPrijs', parseFloat(e.target.value) || 0)}
                          className="ai-chat-input text-sm"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-white/60">Unit: {item.eenheid}</span>
                      <span className="text-white font-medium">Total: €{item.totaalPrijs.toFixed(2)}</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Pricing Summary */}
          <div className="space-y-6">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white flex items-center">
                  <Calculator className="h-5 w-5 mr-2" />
                  Pricing Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-white/60">Base Price</span>
                    <span className="text-white">€{pricing.basisPrijs.toFixed(2)}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-white/60">VAT ({pricing.btwPercentage}%)</span>
                    <span className="text-white">€{pricing.btwBedrag.toFixed(2)}</span>
                  </div>
                  
                  <div className="border-t border-white/20 pt-2">
                    <div className="flex justify-between">
                      <span className="text-white font-medium">Total Price</span>
                      <span className="text-white font-bold text-lg">€{pricing.totaalPrijs.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-white">Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={handleSubmit}
                  disabled={isLoading}
                  className="w-full gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Creating Quote...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Create Quote
                    </>
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => router.push('/dashboard')}
                  className="w-full border-white/20 text-white hover:bg-white/10"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>

                <Button
                  variant="outline"
                  className="w-full border-orange-500/20 text-orange-400 hover:bg-orange-500/10"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate with AI
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}