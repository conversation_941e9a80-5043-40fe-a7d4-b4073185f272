'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  CreditCard, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  Search,
  Plus,
  Download,
  FileText,
  Users
} from 'lucide-react'
import { DashboardLayout } from '@/components/dashboard/layout'

interface Payment {
  id: string
  quoteId: string
  quoteNumber: string
  customerName: string
  amount: number
  status: 'PAID' | 'PENDING' | 'OVERDUE' | 'CANCELLED'
  dueDate: string
  paidDate?: string
  paymentMethod: 'BANK_TRANSFER' | 'CASH' | 'CREDIT_CARD' | 'PAYPAL'
  invoiceNumber?: string
  createdAt: string
}

interface PaymentStats {
  totalRevenue: number
  pendingPayments: number
  overduePayments: number
  monthlyRevenue: number
  revenueGrowth: number
  averagePaymentTime: number
}

export default function PaymentsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [payments, setPayments] = useState<Payment[]>([])
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([])
  const [stats, setStats] = useState<PaymentStats>({
    totalRevenue: 0,
    pendingPayments: 0,
    overduePayments: 0,
    monthlyRevenue: 0,
    revenueGrowth: 0,
    averagePaymentTime: 0
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    loadPayments()
  }, [session, status, router])

  useEffect(() => {
    filterPayments()
  }, [payments, searchTerm, statusFilter])

  const loadPayments = async () => {
    try {
      // Mock payments data
      const mockPayments: Payment[] = [
        {
          id: '1',
          quoteId: 'quote-001',
          quoteNumber: 'Q2024-001',
          customerName: 'Jan Jansen',
          amount: 8500,
          status: 'PAID',
          dueDate: '2024-01-15T00:00:00Z',
          paidDate: '2024-01-10T00:00:00Z',
          paymentMethod: 'BANK_TRANSFER',
          invoiceNumber: 'INV-2024-001',
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          quoteId: 'quote-002',
          quoteNumber: 'Q2024-002',
          customerName: 'Marie de Vries',
          amount: 12500,
          status: 'PENDING',
          dueDate: '2024-01-20T00:00:00Z',
          paymentMethod: 'CREDIT_CARD',
          invoiceNumber: 'INV-2024-002',
          createdAt: '2024-01-05T00:00:00Z'
        },
        {
          id: '3',
          quoteId: 'quote-003',
          quoteNumber: 'Q2024-003',
          customerName: 'Piet Bakker',
          amount: 6800,
          status: 'OVERDUE',
          dueDate: '2024-01-05T00:00:00Z',
          paymentMethod: 'BANK_TRANSFER',
          invoiceNumber: 'INV-2024-003',
          createdAt: '2023-12-20T00:00:00Z'
        },
        {
          id: '4',
          quoteId: 'quote-004',
          quoteNumber: 'Q2024-004',
          customerName: 'Elena Visser',
          amount: 15200,
          status: 'PAID',
          dueDate: '2024-01-25T00:00:00Z',
          paidDate: '2024-01-18T00:00:00Z',
          paymentMethod: 'PAYPAL',
          invoiceNumber: 'INV-2024-004',
          createdAt: '2024-01-10T00:00:00Z'
        }
      ]

      setPayments(mockPayments)
      
      // Calculate stats
      const totalRevenue = mockPayments
        .filter(p => p.status === 'PAID')
        .reduce((sum, p) => sum + p.amount, 0)
      
      const pendingPayments = mockPayments
        .filter(p => p.status === 'PENDING')
        .reduce((sum, p) => sum + p.amount, 0)
      
      const overduePayments = mockPayments
        .filter(p => p.status === 'OVERDUE')
        .reduce((sum, p) => sum + p.amount, 0)
      
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      const monthlyRevenue = mockPayments
        .filter(p => {
          const paidDate = new Date(p.paidDate || p.createdAt)
          return paidDate.getMonth() === currentMonth && paidDate.getFullYear() === currentYear
        })
        .reduce((sum, p) => sum + p.amount, 0)

      setStats({
        totalRevenue,
        pendingPayments,
        overduePayments,
        monthlyRevenue,
        revenueGrowth: 15.2, // Mock growth percentage
        averagePaymentTime: 7.5 // Mock average days
      })
    } catch (error) {
      console.error('Error loading payments:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterPayments = () => {
    let filtered = payments

    if (searchTerm) {
      filtered = filtered.filter(payment =>
        payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.quoteNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.invoiceNumber?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(payment => payment.status === statusFilter)
    }

    setFilteredPayments(filtered)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-500/20 text-green-400'
      case 'PENDING': return 'bg-yellow-500/20 text-yellow-400'
      case 'OVERDUE': return 'bg-red-500/20 text-red-400'
      case 'CANCELLED': return 'bg-gray-500/20 text-gray-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID': return CheckCircle
      case 'PENDING': return Clock
      case 'OVERDUE': return AlertCircle
      case 'CANCELLED': return AlertCircle
      default: return Clock
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'BANK_TRANSFER': return CreditCard
      case 'CREDIT_CARD': return CreditCard
      case 'PAYPAL': return CreditCard
      case 'CASH': return DollarSign
      default: return CreditCard
    }
  }

  const getPaymentMethodColor = (method: string) => {
    switch (method) {
      case 'BANK_TRANSFER': return 'text-blue-400'
      case 'CREDIT_CARD': return 'text-purple-400'
      case 'PAYPAL': return 'text-yellow-400'
      case 'CASH': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
              <CreditCard className="h-8 w-8 text-white" />
            </div>
            <p className="text-white/60">Loading payments...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">Payments</h1>
            <p className="text-white/60 mt-1">
              Track payments, invoices, and revenue for your business
            </p>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline"
              className="text-white border-white/20 hover:bg-white/15"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button 
              className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
              onClick={() => {
                // New payment logic
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Payment
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Revenue
              </CardTitle>
              <DollarSign className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                €{stats.totalRevenue.toLocaleString()}
              </div>
              <div className="flex items-center space-x-1 text-xs">
                <TrendingUp className="h-3 w-3 text-green-400" />
                <span className="text-green-400">
                  +{stats.revenueGrowth}% from last month
                </span>
              </div>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Pending Payments
              </CardTitle>
              <Clock className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                €{stats.pendingPayments.toLocaleString()}
              </div>
              <p className="text-xs text-white/60">Awaiting payment</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Overdue Payments
              </CardTitle>
              <AlertCircle className="h-4 w-4 text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                €{stats.overduePayments.toLocaleString()}
              </div>
              <p className="text-xs text-white/60">Requires attention</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Monthly Revenue
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                €{stats.monthlyRevenue.toLocaleString()}
              </div>
              <p className="text-xs text-white/60">This month</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Search & Filter Payments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-5 w-5" />
                <Input
                  placeholder="Search by customer, quote number, or invoice..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="ai-chat-input pl-12"
                />
              </div>
              <div className="flex gap-2">
                {['ALL', 'PAID', 'PENDING', 'OVERDUE', 'CANCELLED'].map((status) => (
                  <Button
                    key={status}
                    variant={statusFilter === status ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setStatusFilter(status)}
                    className={statusFilter === status ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                  >
                    {status}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payments List */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">
              Payments ({filteredPayments.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredPayments.length === 0 ? (
              <div className="text-center py-12">
                <CreditCard className="h-12 w-12 text-white/20 mx-auto mb-4" />
                <p className="text-white/60 mb-4">
                  {searchTerm || statusFilter !== 'ALL' 
                    ? 'No payments found matching your criteria.' 
                    : 'No payments yet.'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredPayments.map((payment) => {
                  const StatusIcon = getStatusIcon(payment.status)
                  const MethodIcon = getPaymentMethodIcon(payment.paymentMethod)
                  
                  return (
                    <div key={payment.id} className="p-4 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className={`p-3 rounded-lg ${getStatusColor(payment.status)}`}>
                            <StatusIcon className="h-6 w-6" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="text-lg font-semibold text-white">
                                {payment.customerName}
                              </h3>
                              <Badge className={getStatusColor(payment.status)}>
                                {payment.status}
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-white/60">Quote</p>
                                <p className="text-white font-medium">{payment.quoteNumber}</p>
                              </div>
                              <div>
                                <p className="text-white/60">Amount</p>
                                <p className="text-white font-medium">€{payment.amount.toLocaleString()}</p>
                              </div>
                              <div>
                                <p className="text-white/60">Due Date</p>
                                <p className="text-white font-medium">
                                  {new Date(payment.dueDate).toLocaleDateString()}
                                </p>
                              </div>
                              <div>
                                <p className="text-white/60">Payment Method</p>
                                <div className="flex items-center gap-1">
                                  <MethodIcon className={`h-4 w-4 ${getPaymentMethodColor(payment.paymentMethod)}`} />
                                  <span className="text-white font-medium">
                                    {payment.paymentMethod.replace('_', ' ')}
                                  </span>
                                </div>
                              </div>
                            </div>
                            
                            {payment.invoiceNumber && (
                              <div className="flex items-center gap-2 mt-2">
                                <FileText className="h-4 w-4 text-purple-400" />
                                <span className="text-sm text-purple-400">
                                  Invoice: {payment.invoiceNumber}
                                </span>
                              </div>
                            )}
                            
                            {payment.paidDate && (
                              <div className="flex items-center gap-2 mt-1">
                                <CheckCircle className="h-4 w-4 text-green-400" />
                                <span className="text-sm text-green-400">
                                  Paid on {new Date(payment.paidDate).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-white/15"
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-white/15"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-lg font-bold text-white">Payment Methods</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { method: 'Bank Transfer', count: 12, amount: 45000, color: 'text-blue-400' },
                  { method: 'Credit Card', count: 8, amount: 32000, color: 'text-purple-400' },
                  { method: 'PayPal', count: 4, amount: 15000, color: 'text-yellow-400' },
                  { method: 'Cash', count: 2, amount: 8000, color: 'text-green-400' }
                ].map((item) => (
                  <div key={item.method} className="flex items-center justify-between">
                    <span className="text-white/60">{item.method}</span>
                    <div className="text-right">
                      <p className={`font-medium ${item.color}`}>
                        {item.count} payments
                      </p>
                      <p className="text-sm text-white/40">
                        €{item.amount.toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-lg font-bold text-white">Payment Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { status: 'Paid', count: 18, percentage: 72, color: 'text-green-400' },
                  { status: 'Pending', count: 5, percentage: 20, color: 'text-yellow-400' },
                  { status: 'Overdue', count: 2, percentage: 8, color: 'text-red-400' }
                ].map((item) => (
                  <div key={item.status} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-white/60">{item.status}</span>
                      <span className={`font-medium ${item.color}`}>
                        {item.count} ({item.percentage}%)
                      </span>
                    </div>
                    <div className="w-full bg-white/10 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${item.color.replace('text', 'bg')}`}
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-lg font-bold text-white">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full text-white border-white/20 hover:bg-white/15 justify-start"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export All Payments
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full text-white border-white/20 hover:bg-white/15 justify-start"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full text-white border-white/20 hover:bg-white/15 justify-start"
                >
                  <Users className="h-4 w-4 mr-2" />
                  Send Reminders
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}