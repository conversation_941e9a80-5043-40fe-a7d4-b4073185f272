'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Building, 
  MapPin, 
  Home, 
  Search, 
  Plus, 
  Edit,
  Trash2,
  Calendar,
  Users
} from 'lucide-react'
import { DashboardLayout } from '@/components/dashboard/layout'

interface Property {
  id: string
  name: string
  address: string
  description: string
  propertyType: string
  sizeM2: number
  customerName: string
  createdAt: string
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING'
}

export default function PropertiesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [properties, setProperties] = useState<Property[]>([])
  const [filteredProperties, setFilteredProperties] = useState<Property[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    loadProperties()
  }, [session, status, router])

  useEffect(() => {
    if (properties.length > 0) {
      const filtered = properties.filter(property =>
        property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.customerName.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredProperties(filtered)
    }
  }, [searchTerm, properties])

  const loadProperties = async () => {
    try {
      const response = await fetch('/api/properties')
      if (response.ok) {
        const data = await response.json()
        setProperties(data)
        setFilteredProperties(data)
      }
    } catch (error) {
      console.error('Error loading properties:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-500/20 text-green-400'
      case 'INACTIVE': return 'bg-red-500/20 text-red-400'
      case 'PENDING': return 'bg-yellow-500/20 text-yellow-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getPropertyTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'house': return Home
      case 'apartment': return Building
      default: return Building
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
              <Building className="h-8 w-8 text-white" />
            </div>
            <p className="text-white/60">Loading properties...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">Properties</h1>
            <p className="text-white/60 mt-1">
              Manage all your properties and their details
            </p>
          </div>
          <Button 
            className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
            onClick={() => router.push('/properties/new')}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Property
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Properties
              </CardTitle>
              <Building className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{properties.length}</div>
              <p className="text-xs text-white/60">Across all customers</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Active Properties
              </CardTitle>
              <Home className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {properties.filter(p => p.status === 'ACTIVE').length}
              </div>
              <p className="text-xs text-white/60">Currently active</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Size
              </CardTitle>
              <MapPin className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {properties.reduce((acc, p) => acc + (p.sizeM2 || 0), 0).toLocaleString()}m²
              </div>
              <p className="text-xs text-white/60">Combined property size</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Avg. Size
              </CardTitle>
              <Calendar className="h-4 w-4 text-orange-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {properties.length > 0 
                  ? Math.round(properties.reduce((acc, p) => acc + (p.sizeM2 || 0), 0) / properties.length).toLocaleString()
                  : 0}m²
              </div>
              <p className="text-xs text-white/60">Average property size</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">Search Properties</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-5 w-5" />
              <Input
                placeholder="Search properties by name, address, or customer..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="ai-chat-input pl-12"
              />
            </div>
          </CardContent>
        </Card>

        {/* Properties List */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">All Properties</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredProperties.length === 0 ? (
              <div className="text-center py-12">
                <Building className="h-12 w-12 text-white/20 mx-auto mb-4" />
                <p className="text-white/60 mb-4">
                  {searchTerm ? 'No properties found matching your search.' : 'No properties yet.'}
                </p>
                {!searchTerm && (
                  <Button 
                    className="gradient-bg"
                    onClick={() => router.push('/properties/new')}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Property
                  </Button>
                )}
              </div>
            ) : (
              <div className="grid gap-4">
                {filteredProperties.map((property) => {
                  const Icon = getPropertyTypeIcon(property.propertyType)
                  return (
                    <div key={property.id} className="p-4 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className="p-3 rounded-lg bg-blue-500/20">
                            <Icon className="h-6 w-6 text-blue-400" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="text-lg font-semibold text-white">{property.name}</h3>
                              <Badge className={getStatusColor(property.status)}>
                                {property.status}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-white/60 mb-2">
                              <div className="flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                <span>{property.address}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Home className="h-4 w-4" />
                                <span>{property.propertyType}</span>
                              </div>
                              {property.sizeM2 && (
                                <div className="flex items-center gap-1">
                                  <Calendar className="h-4 w-4" />
                                  <span>{property.sizeM2.toLocaleString()}m²</span>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Users className="h-4 w-4 text-green-400" />
                              <span className="text-green-400">{property.customerName}</span>
                            </div>
                            {property.description && (
                              <p className="text-white/60 text-sm mt-2">{property.description}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-white/15"
                            onClick={() => router.push(`/properties/${property.id}/edit`)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-400 hover:bg-red-500/20"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}