'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  MessageSquare, 
  Send, 
  Phone, 
  Mail, 
  WhatsApp, 
  Search, 
  Plus, 
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  FileText
} from 'lucide-react'
import { DashboardLayout } from '@/components/dashboard/layout'

interface Message {
  id: string
  type: 'EMAIL' | 'WHATSAPP' | 'SMS' | 'INTERNAL'
  customerName: string
  customerEmail: string
  customerPhone: string
  subject: string
  content: string
  status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED' | 'DRAFT'
  direction: 'INCOMING' | 'OUTGOING'
  createdAt: string
  readAt?: string
  quoteId?: string
}

interface Conversation {
  id: string
  customerName: string
  customerEmail: string
  customerPhone: string
  lastMessage: string
  lastMessageTime: string
  unreadCount: number
  status: 'ACTIVE' | 'CLOSED'
}

export default function MessagingPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [messageType, setMessageType] = useState<'EMAIL' | 'WHATSAPP'>('EMAIL')
  const [subject, setSubject] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return
    
    if (!session) {
      router.push('/auth/login')
      return
    }

    loadConversations()
  }, [session, status, router])

  const loadConversations = async () => {
    try {
      // Mock data for now
      const mockConversations: Conversation[] = [
        {
          id: '1',
          customerName: 'Jan Jansen',
          customerEmail: '<EMAIL>',
          customerPhone: '+31 6 12345678',
          lastMessage: 'Bedankt voor de offerte, ik kom er op terug',
          lastMessageTime: '2024-01-15T10:30:00Z',
          unreadCount: 0,
          status: 'ACTIVE'
        },
        {
          id: '2',
          customerName: 'Marie de Vries',
          customerEmail: '<EMAIL>',
          customerPhone: '+31 6 87654321',
          lastMessage: 'Wanneer kunnen we beginnen met de renovatie?',
          lastMessageTime: '2024-01-14T15:45:00Z',
          unreadCount: 2,
          status: 'ACTIVE'
        },
        {
          id: '3',
          customerName: 'Piet Bakker',
          customerEmail: '<EMAIL>',
          customerPhone: '+31 6 11223344',
          lastMessage: 'Offerte is geaccepteerd, graag planning',
          lastMessageTime: '2024-01-13T09:15:00Z',
          unreadCount: 0,
          status: 'ACTIVE'
        }
      ]
      setConversations(mockConversations)
    } catch (error) {
      console.error('Error loading conversations:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadMessages = async (conversationId: string) => {
    try {
      // Mock messages for now
      const mockMessages: Message[] = [
        {
          id: '1',
          type: 'EMAIL',
          customerName: 'Jan Jansen',
          customerEmail: '<EMAIL>',
          customerPhone: '+31 6 12345678',
          subject: 'Offerte aanvraag badkamer renovatie',
          content: 'Geachte heer/mevrouw, ik zou graag een offerte willen ontvangen voor het renoveren van mijn badkamer. Het gaat om een complete renovatie inclusief nieuwe tegels, sanitair en verlichting.',
          status: 'READ',
          direction: 'INCOMING',
          createdAt: '2024-01-15T09:00:00Z',
          readAt: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          type: 'EMAIL',
          customerName: 'Jan Jansen',
          customerEmail: '<EMAIL>',
          customerPhone: '+31 6 12345678',
          subject: 'Re: Offerte aanvraag badkamer renovatie',
          content: 'Beste Jan, bedankt voor uw aanvraag. Ik heb een gedetailleerde offerte opgesteld voor de badkamer renovatie. U kunt deze vinden in de bijlage. Ik hoor graag van u.',
          status: 'DELIVERED',
          direction: 'OUTGOING',
          createdAt: '2024-01-15T10:30:00Z',
          quoteId: 'quote-123'
        },
        {
          id: '3',
          type: 'WHATSAPP',
          customerName: 'Jan Jansen',
          customerEmail: '<EMAIL>',
          customerPhone: '+31 6 12345678',
          subject: '',
          content: 'Bedankt voor de offerte, ik kom er op terug',
          status: 'READ',
          direction: 'INCOMING',
          createdAt: '2024-01-15T14:20:00Z'
        }
      ]
      setMessages(mockMessages)
    } catch (error) {
      console.error('Error loading messages:', error)
    }
  }

  const handleConversationSelect = (conversationId: string) => {
    setSelectedConversation(conversationId)
    loadMessages(conversationId)
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) return

    try {
      const conversation = conversations.find(c => c.id === selectedConversation)
      if (!conversation) return

      const newMessageObj: Message = {
        id: Date.now().toString(),
        type: messageType,
        customerName: conversation.customerName,
        customerEmail: conversation.customerEmail,
        customerPhone: conversation.customerPhone,
        subject: subject,
        content: newMessage,
        status: 'SENT',
        direction: 'OUTGOING',
        createdAt: new Date().toISOString()
      }

      setMessages(prev => [...prev, newMessageObj])
      setNewMessage('')
      setSubject('')
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SENT': return 'bg-blue-500/20 text-blue-400'
      case 'DELIVERED': return 'bg-green-500/20 text-green-400'
      case 'READ': return 'bg-purple-500/20 text-purple-400'
      case 'FAILED': return 'bg-red-500/20 text-red-400'
      case 'DRAFT': return 'bg-yellow-500/20 text-yellow-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SENT': return Send
      case 'DELIVERED': return CheckCircle
      case 'READ': return CheckCircle
      case 'FAILED': return AlertCircle
      case 'DRAFT': return Clock
      default: return Clock
    }
  }

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'EMAIL': return Mail
      case 'WHATSAPP': return WhatsApp
      case 'SMS': return Phone
      case 'INTERNAL': return MessageSquare
      default: return MessageSquare
    }
  }

  if (status === 'loading' || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 gradient-bg rounded-2xl flex items-center justify-center mx-auto mb-4 glow-effect animate-spin">
              <MessageSquare className="h-8 w-8 text-white" />
            </div>
            <p className="text-white/60">Loading messages...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white text-glow">Messaging</h1>
            <p className="text-white/60 mt-1">
              Communicate with customers through email, WhatsApp, and more
            </p>
          </div>
          <Button 
            className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
            onClick={() => {
              // New conversation logic
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Conversation
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Total Conversations
              </CardTitle>
              <MessageSquare className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{conversations.length}</div>
              <p className="text-xs text-white/60">Active conversations</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Unread Messages
              </CardTitle>
              <Mail className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {conversations.reduce((acc, c) => acc + c.unreadCount, 0)}
              </div>
              <p className="text-xs text-white/60">Waiting for response</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                WhatsApp Messages
              </CardTitle>
              <WhatsApp className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {messages.filter(m => m.type === 'WHATSAPP').length}
              </div>
              <p className="text-xs text-white/60">Via WhatsApp</p>
            </CardContent>
          </Card>

          <Card className="stat-card">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white/80">
                Email Messages
              </CardTitle>
              <Mail className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {messages.filter(m => m.type === 'EMAIL').length}
              </div>
              <p className="text-xs text-white/60">Via email</p>
            </CardContent>
          </Card>
        </div>

        {/* Messaging Interface */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Conversations List */}
          <Card className="glass-card lg:col-span-1">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-white">Conversations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {conversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                      selectedConversation === conversation.id
                        ? 'bg-blue-500/20 border border-blue-500/30'
                        : 'bg-white/5 hover:bg-white/10'
                    }`}
                    onClick={() => handleConversationSelect(conversation.id)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-white">{conversation.customerName}</h3>
                          {conversation.unreadCount > 0 && (
                            <Badge className="bg-red-500/20 text-red-400 text-xs">
                              {conversation.unreadCount}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-white/60">{conversation.customerEmail}</p>
                      </div>
                      <Badge 
                        className={conversation.status === 'ACTIVE' 
                          ? 'bg-green-500/20 text-green-400 text-xs' 
                          : 'bg-gray-500/20 text-gray-400 text-xs'
                        }
                      >
                        {conversation.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-white/60 truncate">{conversation.lastMessage}</p>
                    <p className="text-xs text-white/40 mt-1">
                      {new Date(conversation.lastMessageTime).toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Messages Area */}
          <Card className="glass-card lg:col-span-2">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-white">
                {selectedConversation ? 'Messages' : 'Select a conversation'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedConversation ? (
                <div className="flex flex-col h-96">
                  {/* Messages List */}
                  <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                    {messages.map((message) => {
                      const StatusIcon = getStatusIcon(message.status)
                      const TypeIcon = getMessageTypeIcon(message.type)
                      
                      return (
                        <div
                          key={message.id}
                          className={`flex ${message.direction === 'OUTGOING' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div className={`max-w-xs lg:max-w-md ${
                            message.direction === 'OUTGOING' 
                              ? 'bg-blue-500/20' 
                              : 'bg-white/10'
                          } rounded-lg p-3`}>
                            <div className="flex items-center gap-2 mb-1">
                              <TypeIcon className="h-3 w-3 text-white/60" />
                              <span className="text-xs text-white/60">
                                {message.type} • {new Date(message.createdAt).toLocaleString()}
                              </span>
                              <StatusIcon className={`h-3 w-3 ${getStatusColor(message.status).split(' ')[1]}`} />
                            </div>
                            {message.subject && (
                              <h4 className="text-sm font-medium text-white mb-1">{message.subject}</h4>
                            )}
                            <p className="text-sm text-white/80">{message.content}</p>
                            {message.quoteId && (
                              <div className="flex items-center gap-1 mt-2">
                                <FileText className="h-3 w-3 text-purple-400" />
                                <span className="text-xs text-purple-400">Quote attached</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>

                  {/* New Message */}
                  <div className="border-t border-white/10 pt-4">
                    <div className="flex gap-2 mb-2">
                      <Button
                        variant={messageType === 'EMAIL' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setMessageType('EMAIL')}
                        className={messageType === 'EMAIL' ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                      >
                        <Mail className="h-4 w-4 mr-1" />
                        Email
                      </Button>
                      <Button
                        variant={messageType === 'WHATSAPP' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setMessageType('WHATSAPP')}
                        className={messageType === 'WHATSAPP' ? 'gradient-bg' : 'text-white hover:bg-white/15'}
                      >
                        <WhatsApp className="h-4 w-4 mr-1" />
                        WhatsApp
                      </Button>
                    </div>
                    
                    {messageType === 'EMAIL' && (
                      <Input
                        placeholder="Subject"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        className="mb-2 ai-chat-input"
                      />
                    )}
                    
                    <Textarea
                      placeholder="Type your message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="mb-2 ai-chat-input resize-none"
                      rows={3}
                    />
                    
                    <Button 
                      className="gradient-bg w-full"
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim()}
                    >
                      <Send className="h-4 w-4 mr-2" />
                      Send Message
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-96">
                  <div className="text-center">
                    <MessageSquare className="h-12 w-12 text-white/20 mx-auto mb-4" />
                    <p className="text-white/60">Select a conversation to start messaging</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}