'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Lightbulb, 
  TrendingUp, 
  Clock, 
  DollarSign,
  FileText,
  Package,
  Calendar,
  Sparkles,
  Loader2,
  X
} from 'lucide-react'

interface Suggestion {
  id: string
  title: string
  description: string
  category: string
  priority: 'high' | 'medium' | 'low'
  confidence: number
  action: {
    type: 'message' | 'quote' | 'template'
    data: any
  }
  timestamp?: Date
}

interface SmartSuggestionsProps {
  onSuggestionSelect?: (suggestion: Suggestion) => void
  context?: {
    customerType?: string
    projectType?: string
    budget?: string
    location?: string
    urgency?: string
  }
  conversationHistory?: Array<{
    role: string
    content: string
  }>
  className?: string
}

const categoryIcons = {
  quote: FileText,
  materials: Package,
  planning: Calendar,
  pricing: DollarSign
}

const priorityColors = {
  high: 'text-red-400 bg-red-500/20 border-red-400/30',
  medium: 'text-yellow-400 bg-yellow-500/20 border-yellow-400/30',
  low: 'text-green-400 bg-green-500/20 border-green-400/30'
}

export function SmartSuggestions({ 
  onSuggestionSelect, 
  context = {},
  conversationHistory = [],
  className = '' 
}: SmartSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [dismissed, setDismissed] = useState<Set<string>>(new Set())

  useEffect(() => {
    fetchSuggestions()
  }, [context, conversationHistory])

  const fetchSuggestions = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/ai/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context,
          conversationHistory,
          type: 'quote'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch suggestions')
      }

      const data = await response.json()
      setSuggestions(data.suggestions || [])
    } catch (err) {
      console.error('Error fetching suggestions:', err)
      setError('Failed to load suggestions')
      
      // Fallback suggestions
      setSuggestions([
        {
          id: 'fallback_1',
          title: 'Prijsinformatie opvragen',
          description: 'Vraag naar actuele prijzen voor materialen en arbeid.',
          category: 'pricing',
          priority: 'medium' as const,
          confidence: 0.8,
          action: {
            type: 'message',
            data: { message: 'Wat zijn de huidige bouwprijzen?' }
          }
        },
        {
          id: 'fallback_2',
          title: 'Projectplanning maken',
          description: 'Laat de AI helpen met een gedetailleerde projectplanning.',
          category: 'planning',
          priority: 'high' as const,
          confidence: 0.9,
          action: {
            type: 'message',
            data: { message: 'Help me met een projectplanning' }
          }
        }
      ])
    } finally {
      setIsLoading(false)
    }
  }

  const handleSuggestionClick = (suggestion: Suggestion) => {
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion)
    }
  }

  const handleDismiss = (suggestionId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setDismissed(prev => new Set([...prev, suggestionId]))
  }

  const visibleSuggestions = suggestions.filter(s => !dismissed.has(s.id))

  if (visibleSuggestions.length === 0 && !isLoading) {
    return null
  }

  return (
    <Card className={`stat-card ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-white flex items-center gap-3 text-lg">
          <Lightbulb className="h-5 w-5 text-yellow-400" />
          Slimme Suggesties
          <Badge variant="outline" className="ml-auto text-yellow-300 border-yellow-400/30">
            AI Powered
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-purple-400 mr-2" />
            <span className="text-white/60">AI generates suggestions...</span>
          </div>
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-red-400 text-sm mb-2">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchSuggestions}
              className="text-xs"
            >
              Retry
            </Button>
          </div>
        ) : (
          <ScrollArea className="max-h-96">
            <div className="space-y-3">
              {visibleSuggestions.map((suggestion) => {
                const Icon = categoryIcons[suggestion.category as keyof typeof categoryIcons] || Sparkles
                const priorityColor = priorityColors[suggestion.priority]
                
                return (
                  <div
                    key={suggestion.id}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="group relative p-4 rounded-xl glass-card-hover hover:bg-white/10 transition-all duration-200 cursor-pointer"
                  >
                    <button
                      onClick={(e) => handleDismiss(suggestion.id, e)}
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity text-white/40 hover:text-white/60"
                    >
                      <X className="h-4 w-4" />
                    </button>
                    
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center glow-effect group-hover:scale-110 transition-transform duration-200">
                        <Icon className="h-5 w-5 text-white" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-white font-medium text-sm group-hover:text-yellow-300 transition-colors">
                            {suggestion.title}
                          </h4>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${priorityColor}`}
                          >
                            {suggestion.priority}
                          </Badge>
                        </div>
                        
                        <p className="text-white/60 text-xs mb-2 line-clamp-2">
                          {suggestion.description}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-white/40 capitalize">
                              {suggestion.category}
                            </span>
                            <div className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                              <span className="text-xs text-green-400">
                                {Math.round(suggestion.confidence * 100)}%
                              </span>
                            </div>
                          </div>
                          
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <TrendingUp className="h-4 w-4 text-blue-400" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        )}
        
        {visibleSuggestions.length > 0 && (
          <div className="mt-4 pt-4 border-t border-white/10">
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchSuggestions}
              className="w-full text-white/60 hover:text-white hover:bg-white/10"
              disabled={isLoading}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {isLoading ? 'Loading...' : 'Refresh Suggestions'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}