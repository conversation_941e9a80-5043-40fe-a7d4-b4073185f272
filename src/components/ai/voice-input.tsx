'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react'

interface VoiceInputProps {
  onTranscript: (transcript: string) => void
  disabled?: boolean
  className?: string
}

export function VoiceInput({ onTranscript, disabled = false, className = '' }: VoiceInputProps) {
  const [isListening, setIsListening] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const recognitionRef = useRef<SpeechRecognition | null>(null)

  useEffect(() => {
    // Initialize speech recognition
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition
      
      if (SpeechRecognition) {
        recognitionRef.current = new SpeechRecognition()
        recognitionRef.current.continuous = false
        recognitionRef.current.interimResults = false
        recognitionRef.current.lang = 'nl-NL'

        recognitionRef.current.onresult = (event) => {
          const transcript = event.results[0][0].transcript
          setIsProcessing(false)
          setIsListening(false)
          onTranscript(transcript.trim())
        }

        recognitionRef.current.onerror = (event) => {
          console.error('Speech recognition error:', event.error)
          setError(`Speech recognition error: ${event.error}`)
          setIsProcessing(false)
          setIsListening(false)
        }

        recognitionRef.current.onend = () => {
          setIsListening(false)
          setIsProcessing(false)
        }
      }
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [onTranscript])

  const toggleListening = () => {
    if (!recognitionRef.current) {
      setError('Speech recognition not supported in this browser')
      return
    }

    if (isListening) {
      recognitionRef.current.stop()
      setIsListening(false)
      setIsProcessing(false)
    } else {
      setError(null)
      setIsProcessing(true)
      
      try {
        recognitionRef.current.start()
        setIsListening(true)
      } catch (err) {
        console.error('Error starting speech recognition:', err)
        setError('Failed to start speech recognition')
        setIsProcessing(false)
      }
    }
  }

  const isSupported = typeof window !== 'undefined' && (
    window.SpeechRecognition || (window as any).webkitSpeechRecognition
  )

  if (!isSupported) {
    return (
      <Button
        variant="ghost"
        size="sm"
        disabled={true}
        className={`${className} text-gray-400`}
        title="Voice input not supported"
      >
        <MicOff className="h-5 w-5" />
      </Button>
    )
  }

  return (
    <div className="relative">
      <Button
        onClick={toggleListening}
        variant="ghost"
        size="sm"
        disabled={disabled || isProcessing}
        className={`
          ${className}
          ${isListening ? 'bg-red-500/20 text-red-400 animate-pulse' : 'text-white hover:bg-white/15'}
          transition-all duration-200
        `}
        title={isListening ? 'Stop recording' : 'Start voice input'}
      >
        {isProcessing ? (
          <Loader2 className="h-5 w-5 animate-spin" />
        ) : isListening ? (
          <MicOff className="h-5 w-5" />
        ) : (
          <Mic className="h-5 w-5" />
        )}
      </Button>
      
      {error && (
        <div className="absolute top-full mt-2 right-0 bg-red-500/20 text-red-300 text-xs p-2 rounded-lg border border-red-400/30 whitespace-nowrap z-50">
          {error}
        </div>
      )}
      
      {isListening && (
        <div className="absolute top-full mt-2 right-0 bg-green-500/20 text-green-300 text-xs p-2 rounded-lg border border-green-400/30 whitespace-nowrap z-50">
          Listening...
        </div>
      )}
    </div>
  )
}