'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  FileText, 
  Calculator, 
  Clock, 
  CheckCircle,
  Euro,
  Loader2,
  Sparkles,
  User,
  MapPin,
  DollarSign,
  Calendar,
  Package,
  Wrench
} from 'lucide-react'

interface QuoteItem {
  description: string
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number
  category: string
}

interface GeneratedQuote {
  title: string
  description: string
  items: QuoteItem[]
  subtotal: number
  vatRate: number
  vatAmount: number
  total: number
  terms: string[]
  notes: string[]
  validity: string
  estimatedDuration: string
}

interface QuoteGeneratorProps {
  onQuoteGenerated?: (quote: GeneratedQuote) => void
  className?: string
}

export function QuoteGenerator({ onQuoteGenerated, className = '' }: QuoteGeneratorProps) {
  const [projectDescription, setProjectDescription] = useState('')
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    type: 'particulier',
    location: '',
    budget: ''
  })
  const [requirements, setRequirements] = useState({
    materials: '',
    services: '',
    timeline: '',
    specialRequests: ''
  })
  const [generatedQuote, setGeneratedQuote] = useState<GeneratedQuote | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleGenerateQuote = async () => {
    if (!projectDescription.trim()) {
      setError('Projectbeschrijving is verplicht')
      return
    }

    setIsGenerating(true)
    setError(null)

    try {
      const response = await fetch('/api/ai/quote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectDescription: projectDescription.trim(),
          customerInfo: {
            name: customerInfo.name || undefined,
            type: customerInfo.type || undefined,
            location: customerInfo.location || undefined,
            budget: customerInfo.budget || undefined
          },
          requirements: {
            materials: requirements.materials ? requirements.materials.split(',').map(m => m.trim()) : undefined,
            services: requirements.services ? requirements.services.split(',').map(s => s.trim()) : undefined,
            timeline: requirements.timeline || undefined,
            specialRequests: requirements.specialRequests || undefined
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate quote')
      }

      const data = await response.json()
      setGeneratedQuote(data.quote)
      
      if (onQuoteGenerated) {
        onQuoteGenerated(data.quote)
      }
    } catch (err) {
      console.error('Error generating quote:', err)
      setError('Er is iets misgegaan bij het genereren van de offerte')
    } finally {
      setIsGenerating(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('nl-NL', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'materialen':
        return <Package className="h-4 w-4" />
      case 'arbeid':
        return <Wrench className="h-4 w-4" />
      default:
        return <Euro className="h-4 w-4" />
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Input Form */}
      <Card className="stat-card">
        <CardHeader className="pb-4">
          <CardTitle className="text-white flex items-center gap-3 text-xl">
            <Calculator className="h-6 w-6 text-green-400" />
            AI Offerte Generator
            <Badge variant="outline" className="ml-auto text-green-300 border-green-400/30">
              Automated
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Project Description */}
          <div>
            <Label htmlFor="projectDescription" className="text-white font-medium mb-2 block">
              Projectbeschrijving *
            </Label>
            <Textarea
              id="projectDescription"
              value={projectDescription}
              onChange={(e) => setProjectDescription(e.target.value)}
              placeholder="Beschrijf het project in detail... (bijv. Complete badkamerrenovatie met nieuwe tegels, sanitair en verlichting)"
              className="ai-chat-input resize-none"
              rows={3}
            />
          </div>

          <Separator className="border-white/20" />

          {/* Customer Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customerName" className="text-white font-medium mb-2 block">
                Klantnaam
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                <Input
                  id="customerName"
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Naam klant"
                  className="ai-chat-input pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="customerType" className="text-white font-medium mb-2 block">
                Klanttype
              </Label>
              <select
                id="customerType"
                value={customerInfo.type}
                onChange={(e) => setCustomerInfo(prev => ({ ...prev, type: e.target.value }))}
                className="ai-chat-input w-full"
              >
                <option value="particulier">Particulier</option>
                <option value="zakelijk">Zakelijk</option>
              </select>
            </div>
            <div>
              <Label htmlFor="location" className="text-white font-medium mb-2 block">
                Locatie
              </Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                <Input
                  id="location"
                  value={customerInfo.location}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="Plaats, provincie"
                  className="ai-chat-input pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="budget" className="text-white font-medium mb-2 block">
                Budgetindicatie
              </Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                <Input
                  id="budget"
                  value={customerInfo.budget}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, budget: e.target.value }))}
                  placeholder="bijv. €5.000 - €10.000"
                  className="ai-chat-input pl-10"
                />
              </div>
            </div>
          </div>

          {/* Requirements */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="materials" className="text-white font-medium mb-2 block">
                Gewenste materialen
              </Label>
              <Input
                id="materials"
                value={requirements.materials}
                onChange={(e) => setRequirements(prev => ({ ...prev, materials: e.target.value }))}
                placeholder="Tegelwerk, hout, staal, etc. (gescheiden door komma's)"
                className="ai-chat-input"
              />
            </div>
            <div>
              <Label htmlFor="services" className="text-white font-medium mb-2 block">
                Gewenste diensten
              </Label>
              <Input
                id="services"
                value={requirements.services}
                onChange={(e) => setRequirements(prev => ({ ...prev, services: e.target.value }))}
                placeholder="Installatie, montage, ontwerp, etc. (gescheiden door komma's)"
                className="ai-chat-input"
              />
            </div>
            <div>
              <Label htmlFor="timeline" className="text-white font-medium mb-2 block">
                Gewenste tijdlijn
              </Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
                <Input
                  id="timeline"
                  value={requirements.timeline}
                  onChange={(e) => setRequirements(prev => ({ ...prev, timeline: e.target.value }))}
                  placeholder="bijv. Binnen 2 maanden"
                  className="ai-chat-input pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="specialRequests" className="text-white font-medium mb-2 block">
                Speciale wensen
              </Label>
              <Input
                id="specialRequests"
                value={requirements.specialRequests}
                onChange={(e) => setRequirements(prev => ({ ...prev, specialRequests: e.target.value }))}
                placeholder="Duurzaam, luxe uitvoering, etc."
                className="ai-chat-input"
              />
            </div>
          </div>

          {error && (
            <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-3">
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          )}

          <Button
            onClick={handleGenerateQuote}
            disabled={isGenerating || !projectDescription.trim()}
            className="w-full gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                AI genereert offerte...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Genereer Offerte
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Generated Quote */}
      {generatedQuote && (
        <Card className="stat-card">
          <CardHeader className="pb-4">
            <CardTitle className="text-white flex items-center gap-3 text-xl">
              <FileText className="h-6 w-6 text-blue-400" />
              {generatedQuote.title}
              <Badge variant="outline" className="ml-auto text-blue-300 border-blue-400/30">
                <CheckCircle className="h-3 w-3 mr-1" />
                Generated
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Quote Header */}
            <div className="space-y-3">
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-yellow-400" />
                  <span className="text-white/80">Geschatte duur:</span>
                  <span className="text-white font-medium">{generatedQuote.estimatedDuration}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-green-400" />
                  <span className="text-white/80">Geldigheid:</span>
                  <span className="text-white font-medium">{generatedQuote.validity}</span>
                </div>
              </div>
              <p className="text-white/70 text-sm">{generatedQuote.description}</p>
            </div>

            {/* Quote Items */}
            <div>
              <h4 className="text-white font-medium mb-3">Offerteposten</h4>
              <ScrollArea className="max-h-64">
                <div className="space-y-2">
                  {generatedQuote.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg glass-card-hover">
                      <div className="flex items-center gap-3 flex-1">
                        <div className="text-blue-400">
                          {getCategoryIcon(item.category)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-white font-medium text-sm truncate">
                            {item.description}
                          </div>
                          <div className="text-white/50 text-xs">
                            {item.quantity} {item.unit} × {formatCurrency(item.unitPrice)}
                          </div>
                        </div>
                      </div>
                      <div className="text-white font-medium">
                        {formatCurrency(item.totalPrice)}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Quote Summary */}
            <div className="border-t border-white/20 pt-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-white/70">Subtotaal:</span>
                <span className="text-white">{formatCurrency(generatedQuote.subtotal)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-white/70">BTW ({generatedQuote.vatRate}%):</span>
                <span className="text-white">{formatCurrency(generatedQuote.vatAmount)}</span>
              </div>
              <Separator className="border-white/20 my-2" />
              <div className="flex justify-between text-lg font-bold">
                <span className="text-white">Totaal:</span>
                <span className="gradient-text">{formatCurrency(generatedQuote.total)}</span>
              </div>
            </div>

            {/* Terms and Notes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-white font-medium mb-2">Voorwaarden</h4>
                <ul className="space-y-1">
                  {generatedQuote.terms.map((term, index) => (
                    <li key={index} className="text-white/60 text-sm flex items-start gap-2">
                      <span className="text-blue-400 mt-1">•</span>
                      {term}
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Opmerkingen</h4>
                <ul className="space-y-1">
                  {generatedQuote.notes.map((note, index) => (
                    <li key={index} className="text-white/60 text-sm flex items-start gap-2">
                      <span className="text-green-400 mt-1">•</span>
                      {note}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4 border-t border-white/20">
              <Button className="flex-1 gradient-bg hover:shadow-lg transition-all duration-300">
                <FileText className="h-4 w-4 mr-2" />
                Opslaan als Offerte
              </Button>
              <Button variant="outline" className="flex-1 border-white/20 text-white hover:bg-white/10">
                PDF Downloaden
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}