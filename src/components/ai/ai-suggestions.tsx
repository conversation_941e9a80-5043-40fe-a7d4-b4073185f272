'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  <PERSON>rkles, 
  TrendingUp, 
  Users, 
  FileText, 
  Target,
  Lightbulb,
  ArrowRight,
  RefreshCw
} from 'lucide-react'

interface AISuggestion {
  id: string
  type: 'QUOTE' | 'CUSTOMER' | 'REVENUE' | 'EFFICIENCY' | 'GROWTH'
  title: string
  description: string
  action: string
  priority: 'HIGH' | 'MEDIUM' | 'LOW'
  confidence: number
  data?: any
}

interface AISuggestionsProps {
  organizationId: string
}

export default function AISuggestions({ organizationId }: AISuggestionsProps) {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date())

  useEffect(() => {
    loadSuggestions()
  }, [organizationId])

  const loadSuggestions = async () => {
    setIsLoading(true)
    try {
      // In a real implementation, this would call an AI service
      // For now, we'll generate mock suggestions based on typical business patterns
      const mockSuggestions = await generateMockSuggestions()
      setSuggestions(mockSuggestions)
      setLastRefreshed(new Date())
    } catch (error) {
      console.error('Error loading AI suggestions:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const generateMockSuggestions = async (): Promise<AISuggestion[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    const baseSuggestions: AISuggestion[] = [
      {
        id: '1',
        type: 'REVENUE',
        title: 'Increase Quote Conversion Rate',
        description: 'Your current quote acceptance rate is 75%. Industry average is 85%. Consider following up on pending quotes.',
        action: 'View Pending Quotes',
        priority: 'HIGH',
        confidence: 92,
        data: { currentRate: 75, targetRate: 85, pendingQuotes: 3 }
      },
      {
        id: '2',
        type: 'CUSTOMER',
        title: 'Follow Up with Recent Customers',
        description: '5 customers haven\'t been contacted in the last 30 days. Reach out to maintain relationships.',
        action: 'Contact Customers',
        priority: 'MEDIUM',
        confidence: 88,
        data: { inactiveCustomers: 5, daysSinceContact: 30 }
      },
      {
        id: '3',
        type: 'QUOTE',
        title: 'Optimize Pricing Strategy',
        description: 'Your average quote value is €12,100. Consider upselling additional services to increase revenue.',
        action: 'Review Pricing',
        priority: 'MEDIUM',
        confidence: 78,
        data: { averageQuoteValue: 12100, potentialIncrease: 15 }
      },
      {
        id: '4',
        type: 'EFFICIENCY',
        title: 'Automate Quote Generation',
        description: 'You\'re creating 8 quotes per week manually. AI automation could save you 6 hours weekly.',
        action: 'Enable AI Features',
        priority: 'HIGH',
        confidence: 95,
        data: { weeklyQuotes: 8, timeSaved: 6, automationPotential: 75 }
      },
      {
        id: '5',
        type: 'GROWTH',
        title: 'Expand Service Offerings',
        description: 'Based on market trends, consider adding renovation consulting to your services.',
        action: 'Explore New Services',
        priority: 'LOW',
        confidence: 72,
        data: { marketDemand: 85, competitionLevel: 'medium' }
      }
    ]

    // Randomly select 3 suggestions to show
    const shuffled = [...baseSuggestions].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, 3)
  }

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'REVENUE': return TrendingUp
      case 'CUSTOMER': return Users
      case 'QUOTE': return FileText
      case 'EFFICIENCY': return Target
      case 'GROWTH': return Sparkles
      default: return Lightbulb
    }
  }

  const getSuggestionColor = (type: string) => {
    switch (type) {
      case 'REVENUE': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'CUSTOMER': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'QUOTE': return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      case 'EFFICIENCY': return 'bg-orange-500/20 text-orange-400 border-orange-500/30'
      case 'GROWTH': return 'bg-pink-500/20 text-pink-400 border-pink-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'bg-red-500/20 text-red-400'
      case 'MEDIUM': return 'bg-yellow-500/20 text-yellow-400'
      case 'LOW': return 'bg-green-500/20 text-green-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  const handleAction = (suggestion: AISuggestion) => {
    // Handle different actions based on suggestion type
    switch (suggestion.type) {
      case 'QUOTE':
        // Navigate to quotes page
        window.location.href = '/quotes'
        break
      case 'CUSTOMER':
        // Navigate to customers page
        window.location.href = '/customers'
        break
      case 'REVENUE':
        // Navigate to analytics or quotes
        window.location.href = '/quotes'
        break
      case 'EFFICIENCY':
        // Navigate to AI tools
        window.location.href = '/ai-tools'
        break
      case 'GROWTH':
        // Navigate to analytics or settings
        window.location.href = '/analytics'
        break
      default:
        break
    }
  }

  if (isLoading) {
    return (
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white flex items-center justify-between">
            <div className="flex items-center">
              <Sparkles className="h-5 w-5 mr-2" />
              AI Suggestions
            </div>
            <RefreshCw className="h-4 w-4 text-white/40 animate-spin" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="p-4 rounded-lg bg-white/5 animate-pulse">
                <div className="h-4 bg-white/20 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-white/10 rounded w-full mb-2"></div>
                <div className="h-3 bg-white/10 rounded w-5/6"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass-card">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-white flex items-center justify-between">
          <div className="flex items-center">
            <Sparkles className="h-5 w-5 mr-2" />
            AI Suggestions
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadSuggestions}
            className="border-white/20 text-white hover:bg-white/10"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardTitle>
        <p className="text-white/60 text-sm">
          Personalized recommendations to grow your business • Last updated: {lastRefreshed.toLocaleTimeString()}
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {suggestions.length === 0 ? (
            <div className="text-center py-8">
              <Lightbulb className="h-12 w-12 text-white/20 mx-auto mb-4" />
              <p className="text-white/60">No AI suggestions available at the moment.</p>
              <Button
                variant="outline"
                onClick={loadSuggestions}
                className="mt-4 border-white/20 text-white hover:bg-white/10"
              >
                Generate Suggestions
              </Button>
            </div>
          ) : (
            suggestions.map((suggestion) => {
              const Icon = getSuggestionIcon(suggestion.type)
              return (
                <div
                  key={suggestion.id}
                  className={`p-4 rounded-lg border ${getSuggestionColor(suggestion.type)} bg-white/5 hover:bg-white/10 transition-all duration-200 cursor-pointer group`}
                  onClick={() => handleAction(suggestion)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${getSuggestionColor(suggestion.type)}`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div>
                        <h4 className="text-white font-medium group-hover:text-blue-300 transition-colors">
                          {suggestion.title}
                        </h4>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="secondary" className={`text-xs ${getPriorityColor(suggestion.priority)}`}>
                            {suggestion.priority} PRIORITY
                          </Badge>
                          <span className="text-white/40 text-xs">
                            {suggestion.confidence}% confidence
                          </span>
                        </div>
                      </div>
                    </div>
                    <ArrowRight className="h-4 w-4 text-white/40 group-hover:text-white transition-colors" />
                  </div>
                  
                  <p className="text-white/60 text-sm mb-3 leading-relaxed">
                    {suggestion.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-white/20 text-white hover:bg-white/10 text-xs"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleAction(suggestion)
                      }}
                    >
                      {suggestion.action}
                      <ArrowRight className="h-3 w-3 ml-1" />
                    </Button>
                    
                    {suggestion.data && (
                      <div className="text-xs text-white/40">
                        AI-powered insight
                      </div>
                    )}
                  </div>
                </div>
              )
            })
          )}
        </div>
        
        {suggestions.length > 0 && (
          <div className="mt-6 pt-4 border-t border-white/10">
            <div className="flex items-center justify-between text-sm">
              <span className="text-white/40">
                AI suggestions are generated based on your business data and industry trends
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={loadSuggestions}
                className="text-white/60 hover:text-white text-xs"
              >
                View More Suggestions
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}