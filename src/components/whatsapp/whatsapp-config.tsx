'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  WhatsApp, 
  Settings, 
  TestTube, 
  Save, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  RefreshCw,
  Shield,
  Zap,
  Smartphone,
  Globe,
  Key,
  Eye,
  EyeOff,
  MessageSquare,
  Users,
  Send,
  Clock,
  FileText,
  QrCode,
  Link,
  Copy,
  Plus,
  Edit
} from 'lucide-react'

interface WhatsAppConfig {
  enabled: boolean
  api_token: string
  phone_number_id: string
  business_account_id: string
  webhook_url: string
  webhook_verify_token: string
  template_message_limit: number
  session_timeout: number
  enable_auto_replies: boolean
  enable_message_templates: boolean
  default_template_language: string
  business_hours: {
    monday: { open: string; close: string; enabled: boolean }
    tuesday: { open: string; close: string; enabled: boolean }
    wednesday: { open: string; close: string; enabled: boolean }
    thursday: { open: string; close: string; enabled: boolean }
    friday: { open: string; close: string; enabled: boolean }
    saturday: { open: string; close: string; enabled: boolean }
    sunday: { open: string; close: string; enabled: boolean }
  }
  auto_reply_message: string
  away_message: string
}

interface WhatsAppTemplate {
  id: string
  name: string
  category: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION'
  language: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  components: {
    type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS'
    text: string
    format?: 'TEXT' | 'IMAGE' | 'VIDEO' | 'DOCUMENT'
  }[]
  created_at: string
}

interface TestMessageResult {
  success: boolean
  message: string
  timestamp: string
  details?: string
  message_id?: string
}

const defaultConfig: WhatsAppConfig = {
  enabled: false,
  api_token: '',
  phone_number_id: '',
  business_account_id: '',
  webhook_url: '',
  webhook_verify_token: '',
  template_message_limit: 1000,
  session_timeout: 24,
  enable_auto_replies: true,
  enable_message_templates: true,
  default_template_language: 'nl',
  business_hours: {
    monday: { open: '09:00', close: '17:00', enabled: true },
    tuesday: { open: '09:00', close: '17:00', enabled: true },
    wednesday: { open: '09:00', close: '17:00', enabled: true },
    thursday: { open: '09:00', close: '17:00', enabled: true },
    friday: { open: '09:00', close: '17:00', enabled: true },
    saturday: { open: '10:00', close: '16:00', enabled: false },
    sunday: { open: '10:00', close: '16:00', enabled: false }
  },
  auto_reply_message: 'Bedankt voor je bericht! We nemen zo spoedig mogelijk contact met je op.',
  away_message: 'We zijn momenteel gesloten. We nemen morgen contact met je op tijdens openingstijden.'
}

const mockTemplates: WhatsAppTemplate[] = [
  {
    id: 'template_1',
    name: 'Offerte Bevestiging',
    category: 'UTILITY',
    language: 'nl',
    status: 'APPROVED',
    components: [
      { type: 'HEADER', text: 'Uw offerte is ontvangen' },
      { type: 'BODY', text: 'Beste {{1}}, uw offerte voor {{2}} is ontvangen. Het totale bedrag is €{{3}}. De offerte is geldig tot {{4}}.' },
      { type: 'FOOTER', text: '{{1}}' }
    ],
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'template_2',
    name: 'Afspraak Herinnering',
    category: 'UTILITY',
    language: 'nl',
    status: 'APPROVED',
    components: [
      { type: 'HEADER', text: 'Herinnering: uw afspraak' },
      { type: 'BODY', text: 'Beste {{1}}, dit is een herinnering voor uw afspraak op {{2}} om {{3}}. Locatie: {{4}}.' },
      { type: 'FOOTER', text: '{{1}}' }
    ],
    created_at: '2024-01-02T00:00:00Z'
  }
]

interface WhatsAppConfigProps {
  onConfigSave?: (config: WhatsAppConfig) => void
  className?: string
}

export function WhatsAppConfig({ onConfigSave, className = '' }: WhatsAppConfigProps) {
  const [config, setConfig] = useState<WhatsAppConfig>(defaultConfig)
  const [templates, setTemplates] = useState<WhatsAppTemplate[]>(mockTemplates)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<TestMessageResult | null>(null)
  const [showApiToken, setShowApiToken] = useState(false)
  const [activeTab, setActiveTab] = useState('basic')
  const [testPhoneNumber, setTestPhoneNumber] = useState('+31 6 ********')

  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    setIsLoading(true)
    try {
      // Simulate API call - in real app, fetch from backend
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Mock config - in real app this would come from backend
      const mockConfig: WhatsAppConfig = {
        ...defaultConfig,
        enabled: true,
        api_token: 'EAAD....',
        phone_number_id: '***************',
        business_account_id: '***************',
        webhook_url: 'https://jouwbedrijf.nl/api/whatsapp/webhook',
        webhook_verify_token: 'secure_verify_token_123'
      }
      
      setConfig(mockConfig)
    } catch (error) {
      console.error('Error loading WhatsApp config:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveConfig = async () => {
    setIsSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (onConfigSave) {
        onConfigSave(config)
      }
      
      // Show success message
      setTestResult({
        success: true,
        message: 'WhatsApp configuratie succesvol opgeslagen',
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error saving config:', error)
      setTestResult({
        success: false,
        message: 'Fout bij opslaan WhatsApp configuratie',
        timestamp: new Date().toISOString(),
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleTestMessage = async () => {
    setIsTesting(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock test result
      const success = Math.random() > 0.2 // 80% success rate for demo
      
      setTestResult({
        success,
        message: success 
          ? 'Test bericht succesvol verstuurd' 
          : 'Test bericht verzenden mislukt',
        timestamp: new Date().toISOString(),
        details: success 
          ? `Bericht verstuurd naar ${testPhoneNumber}` 
          : 'Controleer je API token en telefoonnummer ID',
        message_id: success ? `msg_${Date.now()}` : undefined
      })
    } catch (error) {
      console.error('Error testing WhatsApp:', error)
      setTestResult({
        success: false,
        message: 'Test mislukt',
        timestamp: new Date().toISOString(),
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsTesting(false)
    }
  }

  const updateConfig = (field: keyof WhatsAppConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }))
  }

  const updateBusinessHours = (day: keyof WhatsAppConfig['business_hours'], field: 'open' | 'close' | 'enabled', value: any) => {
    setConfig(prev => ({
      ...prev,
      business_hours: {
        ...prev.business_hours,
        [day]: {
          ...prev.business_hours[day],
          [field]: value
        }
      }
    }))
  }

  const getConnectionStatus = () => {
    if (!config.enabled) {
      return { status: 'disabled', color: 'bg-gray-500/20 text-gray-400', text: 'Uitgeschakeld' }
    }
    
    if (!config.api_token || !config.phone_number_id || !config.business_account_id) {
      return { status: 'incomplete', color: 'bg-yellow-500/20 text-yellow-400', text: 'Onvolledig' }
    }
    
    return { status: 'connected', color: 'bg-green-500/20 text-green-400', text: 'Verbonden' }
  }

  const connectionStatus = getConnectionStatus()

  if (isLoading) {
    return (
      <Card className={`stat-card ${className}`}>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-green-400 mx-auto mb-4" />
            <p className="text-white/60">Loading WhatsApp configuration...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="stat-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-bold text-white flex items-center gap-3">
              <WhatsApp className="h-6 w-6 text-green-400" />
              WhatsApp Business Configuratie
              <Badge className={connectionStatus.color}>
                {connectionStatus.text}
              </Badge>
            </CardTitle>
            <div className="flex gap-2">
              <Button
                onClick={handleTestMessage}
                disabled={isTesting || connectionStatus.status !== 'connected'}
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10"
              >
                {isTesting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                Test Bericht
              </Button>
              <Button
                onClick={handleSaveConfig}
                disabled={isSaving}
                className="gradient-bg"
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Opslaan
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Test Result */}
          {testResult && (
            <div className={`mb-6 p-4 rounded-lg border ${
              testResult.success 
                ? 'bg-green-500/20 border-green-400/30' 
                : 'bg-red-500/20 border-red-400/30'
            }`}>
              <div className="flex items-center gap-3">
                {testResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-400" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-400" />
                )}
                <div>
                  <p className={`font-medium ${
                    testResult.success ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {testResult.message}
                  </p>
                  {testResult.details && (
                    <p className="text-sm text-white/60 mt-1">{testResult.details}</p>
                  )}
                  {testResult.message_id && (
                    <p className="text-xs text-white/40 mt-1">
                      Message ID: {testResult.message_id}
                    </p>
                  )}
                  <p className="text-xs text-white/40 mt-1">
                    {new Date(testResult.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 glass-card">
              <TabsTrigger value="basic" className="text-white data-[state=active]:bg-white/20">
                Basis
              </TabsTrigger>
              <TabsTrigger value="templates" className="text-white data-[state=active]:bg-white/20">
                Templates
              </TabsTrigger>
              <TabsTrigger value="automation" className="text-white data-[state=active]:bg-white/20">
                Automatisering
              </TabsTrigger>
              <TabsTrigger value="advanced" className="text-white data-[state=active]:bg-white/20">
                Geavanceerd
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              {/* Enable/Disable */}
              <div className="flex items-center justify-between p-4 glass-card rounded-lg">
                <div className="flex items-center gap-3">
                  <WhatsApp className="h-6 w-6 text-green-400" />
                  <div>
                    <h3 className="font-semibold text-white">WhatsApp Business</h3>
                    <p className="text-white/60 text-sm">Stuur berichten via WhatsApp Business API</p>
                  </div>
                </div>
                <Switch
                  checked={config.enabled}
                  onCheckedChange={(checked) => updateConfig('enabled', checked)}
                />
              </div>

              {config.enabled && (
                <>
                  {/* API Configuration */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="apiToken" className="text-white font-medium mb-2 block">
                        API Token
                      </Label>
                      <div className="relative">
                        <Input
                          id="apiToken"
                          type={showApiToken ? 'text' : 'password'}
                          value={config.api_token}
                          onChange={(e) => updateConfig('api_token', e.target.value)}
                          placeholder="EAAD..."
                          className="ai-chat-input pr-10"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowApiToken(!showApiToken)}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-white/60 hover:text-white"
                        >
                          {showApiToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="phoneNumberId" className="text-white font-medium mb-2 block">
                        Phone Number ID
                      </Label>
                      <Input
                        id="phoneNumberId"
                        value={config.phone_number_id}
                        onChange={(e) => updateConfig('phone_number_id', e.target.value)}
                        placeholder="***************"
                        className="ai-chat-input"
                      />
                    </div>

                    <div>
                      <Label htmlFor="businessAccountId" className="text-white font-medium mb-2 block">
                        Business Account ID
                      </Label>
                      <Input
                        id="businessAccountId"
                        value={config.business_account_id}
                        onChange={(e) => updateConfig('business_account_id', e.target.value)}
                        placeholder="***************"
                        className="ai-chat-input"
                      />
                    </div>

                    <div>
                      <Label htmlFor="webhookUrl" className="text-white font-medium mb-2 block">
                        Webhook URL
                      </Label>
                      <Input
                        id="webhookUrl"
                        value={config.webhook_url}
                        onChange={(e) => updateConfig('webhook_url', e.target.value)}
                        placeholder="https://jouwbedrijf.nl/api/whatsapp/webhook"
                        className="ai-chat-input"
                      />
                    </div>
                  </div>

                  {/* Test Message */}
                  <Card className="glass-card">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <Smartphone className="h-5 w-5 text-blue-400" />
                        <h3 className="font-semibold text-white">Test Bericht</h3>
                      </div>
                      <div className="flex gap-2">
                        <Input
                          value={testPhoneNumber}
                          onChange={(e) => setTestPhoneNumber(e.target.value)}
                          placeholder="+31 6 ********"
                          className="ai-chat-input flex-1"
                        />
                        <Button
                          onClick={handleTestMessage}
                          disabled={isTesting || !testPhoneNumber.trim()}
                          variant="outline"
                          className="border-white/20 text-white hover:bg-white/10"
                        >
                          {isTesting ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Send className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </TabsContent>

            <TabsContent value="templates" className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">Bericht Templates</h3>
                <Button className="gradient-bg">
                  <Plus className="h-4 w-4 mr-2" />
                  Nieuwe Template
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates.map((template) => (
                  <Card key={template.id} className="glass-card">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-white">{template.name}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={`text-xs ${
                              template.status === 'APPROVED' 
                                ? 'bg-green-500/20 text-green-400' 
                                : template.status === 'PENDING' 
                                ? 'bg-yellow-500/20 text-yellow-400' 
                                : 'bg-red-500/20 text-red-400'
                            }`}>
                              {template.status}
                            </Badge>
                            <Badge className="text-xs bg-blue-500/20 text-blue-400">
                              {template.language}
                            </Badge>
                            <Badge className="text-xs bg-purple-500/20 text-purple-400">
                              {template.category}
                            </Badge>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="space-y-2">
                        {template.components.map((component, index) => (
                          <div key={index} className="text-sm">
                            <span className="text-white/60 text-xs uppercase">{component.type}:</span>
                            <p className="text-white/80 text-xs mt-1 line-clamp-2">
                              {component.text}
                            </p>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="automation" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white font-medium">Automatische Antwoorden</Label>
                      <p className="text-white/60 text-sm">Stuur auto-replies op ontvangen berichten</p>
                    </div>
                    <Switch
                      checked={config.enable_auto_replies}
                      onCheckedChange={(checked) => updateConfig('enable_auto_replies', checked)}
                    />
                  </div>

                  {config.enable_auto_replies && (
                    <div>
                      <Label htmlFor="autoReply" className="text-white font-medium mb-2 block">
                        Auto-Reply Bericht
                      </Label>
                      <Textarea
                        id="autoReply"
                        value={config.auto_reply_message}
                        onChange={(e) => updateConfig('auto_reply_message', e.target.value)}
                        placeholder="Bedankt voor je bericht! We nemen zo spoedig mogelijk contact met je op."
                        className="ai-chat-input"
                        rows={3}
                      />
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white font-medium">Afwezigheidsbericht</Label>
                      <p className="text-white/60 text-sm">Stuur bericht buiten openingstijden</p>
                    </div>
                    <Switch
                      checked={true} // Always enabled for demo
                      disabled={true}
                    />
                  </div>

                  <div>
                    <Label htmlFor="awayMessage" className="text-white font-medium mb-2 block">
                      Afwezigheidsbericht
                    </Label>
                    <Textarea
                      id="awayMessage"
                      value={config.away_message}
                      onChange={(e) => updateConfig('away_message', e.target.value)}
                      placeholder="We zijn momenteel gesloten. We nemen morgen contact met je op tijdens openingstijden."
                      className="ai-chat-input"
                      rows={3}
                    />
                  </div>
                </div>
              </div>

              {/* Business Hours */}
              <Card className="glass-card">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <Clock className="h-5 w-5 text-yellow-400" />
                    <h3 className="font-semibold text-white">Openingstijden</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(config.business_hours).map(([day, hours]) => (
                      <div key={day} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={hours.enabled}
                            onCheckedChange={(checked) => updateBusinessHours(day as keyof typeof config.business_hours, 'enabled', checked)}
                          />
                          <span className="text-white font-medium capitalize">{day}</span>
                        </div>
                        {hours.enabled && (
                          <div className="flex items-center gap-2 text-sm text-white/60">
                            <Input
                              type="time"
                              value={hours.open}
                              onChange={(e) => updateBusinessHours(day as keyof typeof config.business_hours, 'open', e.target.value)}
                              className="ai-chat-input h-8 w-20 text-xs"
                            />
                            <span>-</span>
                            <Input
                              type="time"
                              value={hours.close}
                              onChange={(e) => updateBusinessHours(day as keyof typeof config.business_hours, 'close', e.target.value)}
                              className="ai-chat-input h-8 w-20 text-xs"
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="webhookVerifyToken" className="text-white font-medium mb-2 block">
                    Webhook Verify Token
                  </Label>
                  <Input
                    id="webhookVerifyToken"
                    value={config.webhook_verify_token}
                    onChange={(e) => updateConfig('webhook_verify_token', e.target.value)}
                    placeholder="secure_verify_token_123"
                    className="ai-chat-input"
                  />
                </div>

                <div>
                  <Label htmlFor="templateLimit" className="text-white font-medium mb-2 block">
                    Template Limiet per Dag
                  </Label>
                  <Input
                    id="templateLimit"
                    type="number"
                    value={config.template_message_limit}
                    onChange={(e) => updateConfig('template_message_limit', parseInt(e.target.value))}
                    placeholder="1000"
                    className="ai-chat-input"
                  />
                </div>

                <div>
                  <Label htmlFor="sessionTimeout" className="text-white font-medium mb-2 block">
                    Session Timeout (uren)
                  </Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={config.session_timeout}
                    onChange={(e) => updateConfig('session_timeout', parseInt(e.target.value))}
                    placeholder="24"
                    className="ai-chat-input"
                  />
                </div>

                <div>
                  <Label htmlFor="defaultLanguage" className="text-white font-medium mb-2 block">
                    Standaard Template Taal
                  </Label>
                  <Select 
                    value={config.default_template_language} 
                    onValueChange={(value) => updateConfig('default_template_language', value)}
                  >
                    <SelectTrigger className="ai-chat-input">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="nl">Nederlands</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="de">Deutsch</SelectItem>
                      <SelectItem value="fr">Français</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Status Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="glass-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-2">
                      <MessageSquare className="h-5 w-5 text-blue-400" />
                      <h3 className="font-semibold text-white">Templates</h3>
                    </div>
                    <div className="text-2xl font-bold text-white mb-1">{templates.length}</div>
                    <div className="text-sm text-white/60">
                      {templates.filter(t => t.status === 'APPROVED').length} goedgekeurd
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-2">
                      <Send className="h-5 w-5 text-green-400" />
                      <h3 className="font-semibold text-white">Vandaag Verstuurd</h3>
                    </div>
                    <div className="text-2xl font-bold text-white mb-1">0</div>
                    <div className="text-sm text-white/60">
                      {config.template_message_limit} limiet
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-2">
                      <Users className="h-5 w-5 text-purple-400" />
                      <h3 className="font-semibold text-white">Actieve Chats</h3>
                    </div>
                    <div className="text-2xl font-bold text-white mb-1">0</div>
                    <div className="text-sm text-white/60">
                      Geen actieve gesprekken
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}