'use client'

import { useState } from 'react'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { 
  Home, 
  Building, 
  FileText, 
  MessageSquare, 
  Search, 
  CreditCard, 
  Users, 
  Sparkles, 
  BarChart3, 
  FileBarChart, 
  Settings,
  Menu,
  X
} from 'lucide-react'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

const navigation = [
  { name: 'Dashboard', icon: Home, href: '/dashboard', color: 'text-blue-400' },
  { name: 'Properties', icon: Building, href: '/properties', color: 'text-green-400' },
  { name: 'Quotes', icon: FileText, href: '/quotes', color: 'text-purple-400' },
  { name: 'Messaging', icon: MessageSquare, href: '/messaging', color: 'text-yellow-400' },
  { name: 'Search', icon: Search, href: '/search', color: 'text-pink-400' },
  { name: 'Payments', icon: CreditCard, href: '/payments', color: 'text-indigo-400' },
  { name: 'Customers', icon: Users, href: '/customers', color: 'text-orange-400' },
  { name: 'AI Tools', icon: Sparkles, href: '/ai-tools', color: 'text-cyan-400' },
  { name: 'Analytics', icon: BarChart3, href: '/analytics', color: 'text-emerald-400' },
  { name: 'Reporting', icon: FileBarChart, href: '/reporting', color: 'text-violet-400' },
  { name: 'Settings', icon: Settings, href: '/settings', color: 'text-gray-400' }
]

export function Sidebar({ isOpen, onClose }: SidebarProps) {
  const [activeItem, setActiveItem] = useState('Dashboard')

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden" 
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={cn(
        "fixed left-0 top-0 h-full w-72 glass-card z-50 transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 glow-effect",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/15">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 gradient-bg rounded-xl flex items-center justify-center glow-effect">
                <span className="text-white font-bold text-lg">AI</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white text-glow">Quote.AI</h1>
                <p className="text-xs text-blue-300 font-medium">+ CRM</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="lg:hidden text-white hover:bg-white/15 transition-all duration-200"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = activeItem === item.name
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => {
                    setActiveItem(item.name)
                    onClose()
                  }}
                  className={cn(
                    "sidebar-item w-full text-left group",
                    isActive && "sidebar-item-active"
                  )}
                >
                  <Icon className={`h-5 w-5 ${item.color} group-hover:scale-110 transition-transform duration-200`} />
                  <span className="font-medium group-hover:text-white transition-colors">{item.name}</span>
                  {isActive && (
                    <div className="ml-auto w-2 h-2 bg-blue-400 rounded-full pulse-animation"></div>
                  )}
                </Link>
              )
            })}
          </nav>

          {/* User section */}
          <div className="p-4 border-t border-white/15">
            <div className="glass-card-hover p-4 flex items-center gap-3">
              <div className="w-12 h-12 gradient-bg rounded-full flex items-center justify-center glow-effect">
                <span className="text-white font-bold text-lg">JD</span>
              </div>
              <div className="flex-1">
                <p className="text-sm font-bold text-white">John Doe</p>
                <p className="text-xs text-blue-300"><EMAIL></p>
                <div className="flex items-center gap-1 mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-xs text-green-400 font-medium">Online</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}