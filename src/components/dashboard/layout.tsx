'use client'

import { useState } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { Sidebar } from './sidebar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Menu, Bell, Search, Plus, LogOut } from 'lucide-react'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { data: session } = useSession()

  return (
    <div className="min-h-screen bg-background dark">
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setSidebarOpen(true)}
          className="mobile-menu-btn"
        >
          <Menu className="h-6 w-6" />
        </Button>
      </div>

      {/* Sidebar */}
      <Sidebar isOpen={sidebar<PERSON><PERSON>} onClose={() => setSidebarOpen(false)} />

      {/* Main content */}
      <div className="lg:ml-72">
        {/* Top header */}
        <header className="glass-card border-b border-white/15 sticky top-0 z-30 glow-effect">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative max-w-md w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search quotes, customers, properties..."
                  className="ai-chat-input pl-12 text-base"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/15 relative group">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full p-0 flex items-center justify-center">
                  3
                </Badge>
              </Button>
              
              <Button className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105">
                <Plus className="h-4 w-4 mr-2" />
                New Quote
              </Button>
              
              <div className="hidden sm:flex items-center gap-3 glass-card-hover p-2">
                <div className="w-10 h-10 gradient-bg rounded-full flex items-center justify-center glow-effect">
                  <span className="text-white font-bold text-sm">
                    {session?.user?.name?.charAt(0).toUpperCase() || 'JD'}
                  </span>
                </div>
                <div className="text-sm">
                  <p className="text-white font-bold">{session?.user?.name || 'John Doe'}</p>
                  <p className="text-blue-300 text-xs">{session?.user?.role || 'Admin'}</p>
                </div>
              </div>

              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => signOut()}
                className="text-white hover:bg-white/15 transition-all duration-200"
                title="Sign out"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-4 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  )
}