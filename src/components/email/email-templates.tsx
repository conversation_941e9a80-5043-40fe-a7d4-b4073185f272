'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Mail, 
  FileText, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Download,
  Save,
  Eye,
  Tag,
  Clock,
  CheckCircle,
  AlertCircle,
  Sparkles,
  Loader2
} from 'lucide-react'

interface EmailTemplate {
  id: string
  name: string
  subject: string
  content: string
  templateType: 'QUOTE' | 'INVOICE' | 'REMINDER' | 'WELCOME' | 'FOLLOW_UP'
  isDefault: boolean
  variables: string[]
  createdAt: string
  updatedAt: string
  usageCount: number
}

interface EmailVariable {
  name: string
  description: string
  example: string
  category: 'customer' | 'quote' | 'organization' | 'system'
}

const emailVariables: EmailVariable[] = [
  { name: '{{customer_name}}', description: 'Klantnaam', example: 'Jan Jansen', category: 'customer' },
  { name: '{{customer_email}}', description: 'Klant e-mail', example: '<EMAIL>', category: 'customer' },
  { name: '{{customer_phone}}', description: 'Klant telefoon', example: '+31 6 12345678', category: 'customer' },
  { name: '{{customer_address}}', description: 'Klant adres', example: 'Straat 123, 1234 AB Plaats', category: 'customer' },
  { name: '{{quote_number}}', description: 'Offertenummer', example: '2024-001', category: 'quote' },
  { name: '{{quote_title}}', description: 'Offerte titel', example: 'Badkamer renovatie', category: 'quote' },
  { name: '{{quote_amount}}', description: 'Offerte bedrag', example: '€2.500', category: 'quote' },
  { name: '{{quote_validity}}', description: 'Geldigheid offerte', example: '30 dagen', category: 'quote' },
  { name: '{{organization_name}}', description: 'Bedrijfsnaam', example: 'Jouw Bedrijf BV', category: 'organization' },
  { name: '{{organization_phone}}', description: 'Bedrijfs telefoon', example: '+31 10 1234567', category: 'organization' },
  { name: '{{organization_email}}', description: 'Bedrijfs e-mail', example: '<EMAIL>', category: 'organization' },
  { name: '{{current_date}}', description: 'Huidige datum', example: '15 januari 2024', category: 'system' },
  { name: '{{current_time}}', description: 'Huidige tijd', example: '14:30', category: 'system' }
]

const defaultTemplates: Omit<EmailTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>[] = [
  {
    name: 'Offerte Bevestiging',
    subject: 'Uw offerte {{quote_number}} voor {{quote_title}}',
    content: `Geachte {{customer_name}},

Hartelijk dank voor uw interesse in onze diensten. Bijgevoegd vindt u de offerte voor {{quote_title}}.

**Offertedetails:**
- Offertenummer: {{quote_number}}
- Project: {{quote_title}}
- Totaalbedrag: {{quote_amount}}
- Geldig tot: {{quote_validity}}

De offerte is zorgvuldig opgesteld op basis van uw wensen en eisen. Mocht u vragen hebben of wijzigingen willen doorvoeren, dan horen we graag van u.

U kunt deze offerte accepteren door simpelweg op deze e-mail te reageren of contact met ons op te nemen via {{organization_phone}}.

We kijken uit naar uw reactie.

Met vriendelijke groet,

{{organization_name}}
{{organization_phone}}
{{organization_email}}`,
    templateType: 'QUOTE',
    isDefault: true,
    variables: ['customer_name', 'quote_number', 'quote_title', 'quote_amount', 'quote_validity', 'organization_name', 'organization_phone', 'organization_email']
  },
  {
    name: 'Herinnering Offerte',
    subject: 'Vriendelijke herinnering: uw offerte {{quote_number}}',
    content: `Geachte {{customer_name}},

We wilden u vriendelijk herinneren aan uw offerte {{quote_number}} voor {{quote_title}}.

De offerte is nog geldig tot {{quote_validity}}. We willen u graag helpen met uw project en staan klaar om eventuele vragen te beantwoorden.

Mocht u besluiten om met ons in zee te gaan, dan kunnen we snel starten met de planning.

U kunt de offerte opnieuw bekijken of contact met ons opnemen voor vragen.

Met vriendelijke groet,

{{organization_name}}
{{organization_phone}}
{{organization_email}}`,
    templateType: 'REMINDER',
    isDefault: true,
    variables: ['customer_name', 'quote_number', 'quote_title', 'quote_validity', 'organization_name', 'organization_phone', 'organization_email']
  },
  {
    name: 'Welkom Nieuwe Klant',
    subject: 'Welkom bij {{organization_name}}!',
    content: `Geachte {{customer_name}},

Welkom als nieuwe klant bij {{organization_name}}! We zijn verheugd dat u voor ons heeft gekozen.

Als nieuwe klant kunt u rekenen op:
- Persoonlijke service en aandacht
- Kwalitatieve uitvoering van projecten
- Duidelijke communicatie throughout het proces
- Transparante prijzen en facturatie

Uw contactgegevens:
- Naam: {{customer_name}}
- E-mail: {{customer_email}}
- Telefoon: {{customer_phone}}
- Adres: {{customer_address}}

Mocht u vragen hebben of direct contact willen opnemen, dan kunt ons bereiken op:
- Telefoon: {{organization_phone}}
- E-mail: {{organization_email}}

We kijken uit naar een succesvolle samenwerking!

Met vriendelijke groet,

Het team van {{organization_name}}`,
    templateType: 'WELCOME',
    isDefault: true,
    variables: ['customer_name', 'organization_name', 'customer_email', 'customer_phone', 'customer_address', 'organization_phone', 'organization_email']
  }
]

interface EmailTemplatesProps {
  onTemplateSelect?: (template: EmailTemplate) => void
  className?: string
}

export function EmailTemplates({ onTemplateSelect, className = '' }: EmailTemplatesProps) {
  const [templates, setTemplates] = useState<EmailTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<string>('ALL')

  const [editForm, setEditForm] = useState({
    name: '',
    subject: '',
    content: '',
    templateType: 'QUOTE' as EmailTemplate['templateType'],
    isDefault: false
  })

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    setIsLoading(true)
    try {
      // Simulate API call - in real app, fetch from backend
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const mockTemplates: EmailTemplate[] = defaultTemplates.map((template, index) => ({
        ...template,
        id: `template_${index + 1}`,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        usageCount: Math.floor(Math.random() * 50) + 1
      }))
      
      setTemplates(mockTemplates)
    } catch (error) {
      console.error('Error loading templates:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateTemplate = () => {
    setEditForm({
      name: '',
      subject: '',
      content: '',
      templateType: 'QUOTE',
      isDefault: false
    })
    setIsCreating(true)
    setIsEditing(true)
    setSelectedTemplate(null)
  }

  const handleEditTemplate = (template: EmailTemplate) => {
    setEditForm({
      name: template.name,
      subject: template.subject,
      content: template.content,
      templateType: template.templateType,
      isDefault: template.isDefault
    })
    setSelectedTemplate(template)
    setIsCreating(false)
    setIsEditing(true)
  }

  const handleSaveTemplate = async () => {
    try {
      const templateData = {
        ...editForm,
        id: isCreating ? `template_${Date.now()}` : selectedTemplate?.id,
        variables: extractVariables(editForm.content),
        createdAt: isCreating ? new Date().toISOString() : selectedTemplate?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        usageCount: isCreating ? 0 : selectedTemplate?.usageCount || 0
      }

      if (isCreating) {
        setTemplates(prev => [...prev, templateData as EmailTemplate])
      } else {
        setTemplates(prev => prev.map(t => t.id === selectedTemplate?.id ? templateData as EmailTemplate : t))
      }

      setIsEditing(false)
      setSelectedTemplate(null)
      setIsCreating(false)
    } catch (error) {
      console.error('Error saving template:', error)
    }
  }

  const handleDeleteTemplate = async (templateId: string) => {
    if (confirm('Weet je zeker dat je deze template wilt verwijderen?')) {
      setTemplates(prev => prev.filter(t => t.id !== templateId))
      if (selectedTemplate?.id === templateId) {
        setSelectedTemplate(null)
      }
    }
  }

  const handleDuplicateTemplate = (template: EmailTemplate) => {
    const duplicated = {
      ...template,
      id: `template_${Date.now()}`,
      name: `${template.name} (kopie)`,
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      usageCount: 0
    }
    setTemplates(prev => [...prev, duplicated])
  }

  const extractVariables = (content: string): string[] => {
    const variableRegex = /\{\{([^}]+)\}\}/g
    const matches = content.match(variableRegex)
    return matches ? matches.map(match => match.trim()) : []
  }

  const handleVariableInsert = (variable: string) => {
    setEditForm(prev => ({
      ...prev,
      content: prev.content + variable
    }))
  }

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.subject.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'ALL' || template.templateType === filterType
    return matchesSearch && matchesType
  })

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'QUOTE': return 'bg-blue-500/20 text-blue-400'
      case 'INVOICE': return 'bg-green-500/20 text-green-400'
      case 'REMINDER': return 'bg-yellow-500/20 text-yellow-400'
      case 'WELCOME': return 'bg-purple-500/20 text-purple-400'
      case 'FOLLOW_UP': return 'bg-orange-500/20 text-orange-400'
      default: return 'bg-gray-500/20 text-gray-400'
    }
  }

  if (isLoading) {
    return (
      <Card className={`stat-card ${className}`}>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-400 mx-auto mb-4" />
            <p className="text-white/60">Loading email templates...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="stat-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-bold text-white flex items-center gap-3">
              <Mail className="h-6 w-6 text-blue-400" />
              Email Templates
              <Badge variant="outline" className="text-blue-300 border-blue-400/30">
                {templates.length} templates
              </Badge>
            </CardTitle>
            <Button
              onClick={handleCreateTemplate}
              className="gradient-bg hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              <Plus className="h-4 w-4 mr-2" />
              Nieuwe Template
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filter */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Zoek templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="ai-chat-input"
              />
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="ai-chat-input w-48">
                <SelectValue placeholder="Filter op type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Alle Types</SelectItem>
                <SelectItem value="QUOTE">Offertes</SelectItem>
                <SelectItem value="INVOICE">Facturen</SelectItem>
                <SelectItem value="REMINDER">Herinneringen</SelectItem>
                <SelectItem value="WELCOME">Welkom</SelectItem>
                <SelectItem value="FOLLOW_UP">Follow-up</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map((template) => (
              <Card
                key={template.id}
                className={`glass-card cursor-pointer transition-all duration-200 hover:scale-105 ${
                  selectedTemplate?.id === template.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedTemplate(template)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-white text-lg mb-1">
                        {template.name}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge className={`text-xs ${getTypeColor(template.templateType)}`}>
                          {template.templateType}
                        </Badge>
                        {template.isDefault && (
                          <Badge className="text-xs bg-green-500/20 text-green-400">
                            Standaard
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEditTemplate(template)
                        }}
                        className="h-8 w-8 p-0 text-white/60 hover:text-white"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDuplicateTemplate(template)
                        }}
                        className="h-8 w-8 p-0 text-white/60 hover:text-white"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      {!template.isDefault && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteTemplate(template.id)
                          }}
                          className="h-8 w-8 p-0 text-red-400/60 hover:text-red-400"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-white/80 text-sm mb-3 line-clamp-2">
                    {template.subject}
                  </p>
                  <div className="flex items-center justify-between text-xs text-white/60">
                    <div className="flex items-center gap-2">
                      <FileText className="h-3 w-3" />
                      <span>{template.variables.length} variabelen</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3" />
                      <span>{template.usageCount}x gebruikt</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTemplates.length === 0 && (
            <div className="text-center py-8">
              <Mail className="h-12 w-12 text-white/20 mx-auto mb-4" />
              <p className="text-white/60 mb-4">Geen templates gevonden</p>
              <Button onClick={handleCreateTemplate} className="gradient-bg">
                <Plus className="h-4 w-4 mr-2" />
                Maak eerste template
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Template Editor/Viewer */}
      {isEditing && (
        <Card className="stat-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white">
              {isCreating ? 'Nieuwe Template' : 'Template Bewerken'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Editor Form */}
              <div className="lg:col-span-2 space-y-4">
                <div>
                  <Label htmlFor="templateName" className="text-white font-medium mb-2 block">
                    Template Naam
                  </Label>
                  <Input
                    id="templateName"
                    value={editForm.name}
                    onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Bijv. Offerte Bevestiging"
                    className="ai-chat-input"
                  />
                </div>

                <div>
                  <Label htmlFor="templateSubject" className="text-white font-medium mb-2 block">
                    Onderwerp
                  </Label>
                  <Input
                    id="templateSubject"
                    value={editForm.subject}
                    onChange={(e) => setEditForm(prev => ({ ...prev, subject: e.target.value }))}
                    placeholder="Bijv. Uw offerte {{quote_number}}"
                    className="ai-chat-input"
                  />
                </div>

                <div>
                  <Label htmlFor="templateType" className="text-white font-medium mb-2 block">
                    Type
                  </Label>
                  <Select value={editForm.templateType} onValueChange={(value) => setEditForm(prev => ({ ...prev, templateType: value as EmailTemplate['templateType'] }))}>
                    <SelectTrigger className="ai-chat-input">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="QUOTE">Offerte</SelectItem>
                      <SelectItem value="INVOICE">Factuur</SelectItem>
                      <SelectItem value="REMINDER">Herinnering</SelectItem>
                      <SelectItem value="WELCOME">Welkom</SelectItem>
                      <SelectItem value="FOLLOW_UP">Follow-up</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="templateContent" className="text-white font-medium mb-2 block">
                    Content
                  </Label>
                  <Textarea
                    id="templateContent"
                    value={editForm.content}
                    onChange={(e) => setEditForm(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="Email content met variabelen zoals {{customer_name}}..."
                    className="ai-chat-input min-h-[300px] font-mono text-sm"
                  />
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="isDefault"
                    checked={editForm.isDefault}
                    onChange={(e) => setEditForm(prev => ({ ...prev, isDefault: e.target.checked }))}
                    className="rounded border-white/20 bg-white/10 text-purple-400"
                  />
                  <Label htmlFor="isDefault" className="text-white/80">
                    Standaard template voor dit type
                  </Label>
                </div>
              </div>

              {/* Variables Panel */}
              <div className="space-y-4">
                <div>
                  <h4 className="text-white font-medium mb-3">Beschikbare Variabelen</h4>
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-3">
                      {Object.entries(
                        emailVariables.reduce((acc, variable) => {
                          if (!acc[variable.category]) acc[variable.category] = []
                          acc[variable.category].push(variable)
                          return acc
                        }, {} as Record<string, EmailVariable[]>)
                      ).map(([category, variables]) => (
                        <div key={category}>
                          <h5 className="text-white/60 text-sm font-medium mb-2 uppercase">
                            {category}
                          </h5>
                          <div className="space-y-2">
                            {variables.map((variable) => (
                              <button
                                key={variable.name}
                                onClick={() => handleVariableInsert(variable.name)}
                                className="w-full text-left p-2 rounded glass-card-hover hover:bg-white/10 transition-colors group"
                              >
                                <div className="font-mono text-xs text-blue-400 mb-1">
                                  {variable.name}
                                </div>
                                <div className="text-white/60 text-xs">
                                  {variable.description}
                                </div>
                                <div className="text-white/40 text-xs mt-1">
                                  Voorbeeld: {variable.example}
                                </div>
                              </button>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6 pt-4 border-t border-white/20">
              <Button
                onClick={handleSaveTemplate}
                disabled={!editForm.name || !editForm.subject || !editForm.content}
                className="gradient-bg"
              >
                <Save className="h-4 w-4 mr-2" />
                Opslaan
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false)
                  setSelectedTemplate(null)
                  setIsCreating(false)
                }}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Annuleren
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Template Preview */}
      {selectedTemplate && !isEditing && (
        <Card className="stat-card">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white flex items-center gap-3">
              <Eye className="h-6 w-6 text-green-400" />
              Template Preview: {selectedTemplate.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Template Info */}
              <div className="space-y-4">
                <div>
                  <h4 className="text-white font-medium mb-2">Onderwerp</h4>
                  <p className="text-white/80 bg-white/5 p-3 rounded-lg">
                    {selectedTemplate.subject}
                  </p>
                </div>

                <div>
                  <h4 className="text-white font-medium mb-2">Details</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-white/60">Type:</span>
                      <Badge className={getTypeColor(selectedTemplate.templateType)}>
                        {selectedTemplate.templateType}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">Standaard:</span>
                      {selectedTemplate.isDefault ? (
                        <CheckCircle className="h-4 w-4 text-green-400" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-yellow-400" />
                      )}
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">Gebruikt:</span>
                      <span className="text-white">{selectedTemplate.usageCount} keer</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">Variabelen:</span>
                      <span className="text-white">{selectedTemplate.variables.length}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-white font-medium mb-2">Variabelen</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedTemplate.variables.map((variable, index) => (
                      <Badge key={index} variant="outline" className="text-xs border-blue-400/30 text-blue-400">
                        {variable}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Content Preview */}
              <div>
                <h4 className="text-white font-medium mb-2">Content</h4>
                <ScrollArea className="h-96">
                  <div className="bg-white/5 p-4 rounded-lg">
                    <pre className="text-white/80 whitespace-pre-wrap font-sans text-sm">
                      {selectedTemplate.content}
                    </pre>
                  </div>
                </ScrollArea>
              </div>
            </div>

            <div className="flex gap-3 mt-6 pt-4 border-t border-white/20">
              <Button
                onClick={() => handleEditTemplate(selectedTemplate)}
                className="gradient-bg"
              >
                <Edit className="h-4 w-4 mr-2" />
                Bewerken
              </Button>
              <Button
                variant="outline"
                onClick={() => handleDuplicateTemplate(selectedTemplate)}
                className="border-white/20 text-white hover:bg-white/10"
              >
                <Copy className="h-4 w-4 mr-2" />
                Dupliceren
              </Button>
              {onTemplateSelect && (
                <Button
                  variant="outline"
                  onClick={() => onTemplateSelect(selectedTemplate)}
                  className="border-white/20 text-white hover:bg-white/10"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Gebruiken
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}