'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { 
  Mail, 
  Settings, 
  TestTube, 
  Save, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  RefreshCw,
  Shield,
  Zap,
  Database,
  Smartphone,
  Globe,
  Key,
  Eye,
  EyeOff
} from 'lucide-react'

interface EmailConfig {
  provider: 'gmail' | 'outlook' | 'smtp' | 'sendgrid' | 'resend'
  smtp_host?: string
  smtp_port?: number
  smtp_username?: string
  smtp_password?: string
  from_address: string
  from_name: string
  reply_to?: string
  enable_tracking: boolean
  enable_bcc: boolean
  bcc_address?: string
  max_emails_per_day: number
  enable_dkim: boolean
  dkim_selector?: string
  dkim_private_key?: string
}

interface TestEmailResult {
  success: boolean
  message: string
  timestamp: string
  details?: string
}

const defaultConfig: EmailConfig = {
  provider: 'gmail',
  from_address: '',
  from_name: '',
  reply_to: '',
  enable_tracking: true,
  enable_bcc: false,
  bcc_address: '',
  max_emails_per_day: 100,
  enable_dkim: false,
  dkim_selector: '',
  dkim_private_key: ''
}

const providerConfigs = {
  gmail: {
    name: 'Gmail',
    description: 'Stuur e-mails via Gmail met OAuth2 of App Passwords',
    icon: '📧',
    fields: ['smtp_username', 'smtp_password'],
    defaults: {
      smtp_host: 'smtp.gmail.com',
      smtp_port: 587
    },
    help: 'Gebruik App Passwords voor 2FA accounts of OAuth2 voor enterprise'
  },
  outlook: {
    name: 'Outlook',
    description: 'Microsoft Outlook / Office 365 integratie',
    icon: '🔵',
    fields: ['smtp_username', 'smtp_password'],
    defaults: {
      smtp_host: 'smtp.office365.com',
      smtp_port: 587
    },
    help: 'Ondersteunt zowel persoonlijke als zakelijke Office 365 accounts'
  },
  smtp: {
    name: 'Custom SMTP',
    description: 'Gebruik je eigen SMTP server',
    icon: '🔧',
    fields: ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password'],
    defaults: {},
    help: 'Vereist SMTP server details en authenticatie'
  },
  sendgrid: {
    name: 'SendGrid',
    description: 'Professionele e-mail delivery service',
    icon: '📨',
    fields: ['smtp_username', 'smtp_password'],
    defaults: {
      smtp_host: 'smtp.sendgrid.net',
      smtp_port: 587
    },
    help: 'Gebruik je SendGrid API key als wachtwoord'
  },
  resend: {
    name: 'Resend',
    description: 'Moderne e-mail API voor developers',
    icon: '✉️',
    fields: ['smtp_username', 'smtp_password'],
    defaults: {
      smtp_host: 'smtp.resend.com',
      smtp_port: 587
    },
    help: 'Gebruik je Resend API key als gebruikersnaam'
  }
}

interface EmailConfigProps {
  onConfigSave?: (config: EmailConfig) => void
  className?: string
}

export function EmailConfig({ onConfigSave, className = '' }: EmailConfigProps) {
  const [config, setConfig] = useState<EmailConfig>(defaultConfig)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<TestEmailResult | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [activeTab, setActiveTab] = useState('basic')

  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    setIsLoading(true)
    try {
      // Simulate API call - in real app, fetch from backend
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Mock config - in real app this would come from backend
      const mockConfig: EmailConfig = {
        ...defaultConfig,
        provider: 'gmail',
        from_address: '<EMAIL>',
        from_name: 'Jouw Bedrijf',
        reply_to: '<EMAIL>',
        smtp_username: '<EMAIL>',
        smtp_password: '••••••••',
        max_emails_per_day: 200
      }
      
      setConfig(mockConfig)
    } catch (error) {
      console.error('Error loading email config:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveConfig = async () => {
    setIsSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (onConfigSave) {
        onConfigSave(config)
      }
      
      // Show success message
      setTestResult({
        success: true,
        message: 'Configuratie succesvol opgeslagen',
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error saving config:', error)
      setTestResult({
        success: false,
        message: 'Fout bij opslaan configuratie',
        timestamp: new Date().toISOString(),
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleTestEmail = async () => {
    setIsTesting(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock test result
      const success = Math.random() > 0.2 // 80% success rate for demo
      
      setTestResult({
        success,
        message: success 
          ? 'Test e-mail succesvol verstuurd' 
          : 'Test e-mail verzenden mislukt',
        timestamp: new Date().toISOString(),
        details: success 
          ? 'E-mail is verstuurd naar het testadres' 
          : 'Controleer je SMTP instellingen en probeer het opnieuw'
      })
    } catch (error) {
      console.error('Error testing email:', error)
      setTestResult({
        success: false,
        message: 'Test mislukt',
        timestamp: new Date().toISOString(),
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsTesting(false)
    }
  }

  const handleProviderChange = (provider: EmailConfig['provider']) => {
    const providerConfig = providerConfigs[provider]
    setConfig(prev => ({
      ...prev,
      provider,
      ...providerConfig.defaults,
      // Clear provider-specific fields that aren't needed
      ...(provider === 'gmail' || provider === 'outlook' || provider === 'sendgrid' || provider === 'resend' 
        ? { smtp_host: providerConfig.defaults.smtp_host, smtp_port: providerConfig.defaults.smtp_port }
        : {})
    }))
  }

  const updateConfig = (field: keyof EmailConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }))
  }

  const getProviderStatus = () => {
    if (!config.from_address || !config.from_name) {
      return { status: 'incomplete', color: 'bg-yellow-500/20 text-yellow-400', text: 'Onvolledig' }
    }
    
    if (config.provider === 'smtp' && (!config.smtp_host || !config.smtp_port)) {
      return { status: 'incomplete', color: 'bg-yellow-500/20 text-yellow-400', text: 'SMTP details nodig' }
    }
    
    if (!config.smtp_username || !config.smtp_password) {
      return { status: 'incomplete', color: 'bg-yellow-500/20 text-yellow-400', text: 'Authenticatie nodig' }
    }
    
    return { status: 'configured', color: 'bg-green-500/20 text-green-400', text: 'Geconfigureerd' }
  }

  const providerStatus = getProviderStatus()

  if (isLoading) {
    return (
      <Card className={`stat-card ${className}`}>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-400 mx-auto mb-4" />
            <p className="text-white/60">Loading email configuration...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="stat-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-bold text-white flex items-center gap-3">
              <Settings className="h-6 w-6 text-blue-400" />
              Email Configuratie
              <Badge className={providerStatus.color}>
                {providerStatus.text}
              </Badge>
            </CardTitle>
            <div className="flex gap-2">
              <Button
                onClick={handleTestEmail}
                disabled={isTesting || providerStatus.status === 'incomplete'}
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10"
              >
                {isTesting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                Test E-mail
              </Button>
              <Button
                onClick={handleSaveConfig}
                disabled={isSaving}
                className="gradient-bg"
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Opslaan
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Test Result */}
          {testResult && (
            <div className={`mb-6 p-4 rounded-lg border ${
              testResult.success 
                ? 'bg-green-500/20 border-green-400/30' 
                : 'bg-red-500/20 border-red-400/30'
            }`}>
              <div className="flex items-center gap-3">
                {testResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-400" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-400" />
                )}
                <div>
                  <p className={`font-medium ${
                    testResult.success ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {testResult.message}
                  </p>
                  {testResult.details && (
                    <p className="text-sm text-white/60 mt-1">{testResult.details}</p>
                  )}
                  <p className="text-xs text-white/40 mt-1">
                    {new Date(testResult.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 glass-card">
              <TabsTrigger value="basic" className="text-white data-[state=active]:bg-white/20">
                Basis
              </TabsTrigger>
              <TabsTrigger value="advanced" className="text-white data-[state=active]:bg-white/20">
                Geavanceerd
              </TabsTrigger>
              <TabsTrigger value="security" className="text-white data-[state=active]:bg-white/20">
                Beveiliging
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              {/* Provider Selection */}
              <div>
                <Label className="text-white font-medium mb-3 block">
                  E-mail Provider
                </Label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(providerConfigs).map(([key, provider]) => (
                    <Card
                      key={key}
                      className={`glass-card cursor-pointer transition-all duration-200 hover:scale-105 ${
                        config.provider === key ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handleProviderChange(key as EmailConfig['provider'])}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl mb-2">{provider.icon}</div>
                        <h3 className="font-semibold text-white mb-1">{provider.name}</h3>
                        <p className="text-xs text-white/60">{provider.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Provider Config */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="fromName" className="text-white font-medium mb-2 block">
                    Van Naam
                  </Label>
                  <Input
                    id="fromName"
                    value={config.from_name}
                    onChange={(e) => updateConfig('from_name', e.target.value)}
                    placeholder="Jouw Bedrijf"
                    className="ai-chat-input"
                  />
                </div>

                <div>
                  <Label htmlFor="fromAddress" className="text-white font-medium mb-2 block">
                    Van E-mailadres
                  </Label>
                  <Input
                    id="fromAddress"
                    type="email"
                    value={config.from_address}
                    onChange={(e) => updateConfig('from_address', e.target.value)}
                    placeholder="<EMAIL>"
                    className="ai-chat-input"
                  />
                </div>

                <div>
                  <Label htmlFor="replyTo" className="text-white font-medium mb-2 block">
                    Reply-To (optioneel)
                  </Label>
                  <Input
                    id="replyTo"
                    type="email"
                    value={config.reply_to}
                    onChange={(e) => updateConfig('reply_to', e.target.value)}
                    placeholder="<EMAIL>"
                    className="ai-chat-input"
                  />
                </div>

                <div>
                  <Label htmlFor="maxEmails" className="text-white font-medium mb-2 block">
                    Max E-mails per Dag
                  </Label>
                  <Input
                    id="maxEmails"
                    type="number"
                    value={config.max_emails_per_day}
                    onChange={(e) => updateConfig('max_emails_per_day', parseInt(e.target.value))}
                    placeholder="100"
                    className="ai-chat-input"
                  />
                </div>
              </div>

              {/* Provider-specific fields */}
              {providerConfigs[config.provider].fields.map((field) => (
                <div key={field} className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {field === 'smtp_host' && (
                    <div>
                      <Label htmlFor="smtpHost" className="text-white font-medium mb-2 block">
                        SMTP Host
                      </Label>
                      <Input
                        id="smtpHost"
                        value={config.smtp_host || ''}
                        onChange={(e) => updateConfig('smtp_host', e.target.value)}
                        placeholder="smtp.gmail.com"
                        className="ai-chat-input"
                      />
                    </div>
                  )}
                  
                  {field === 'smtp_port' && (
                    <div>
                      <Label htmlFor="smtpPort" className="text-white font-medium mb-2 block">
                        SMTP Port
                      </Label>
                      <Select 
                        value={config.smtp_port?.toString() || ''} 
                        onValueChange={(value) => updateConfig('smtp_port', parseInt(value))}
                      >
                        <SelectTrigger className="ai-chat-input">
                          <SelectValue placeholder="Selecteer poort" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="25">25 (SMTP)</SelectItem>
                          <SelectItem value="587">587 (TLS)</SelectItem>
                          <SelectItem value="465">465 (SSL)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                  
                  {field === 'smtp_username' && (
                    <div>
                      <Label htmlFor="smtpUsername" className="text-white font-medium mb-2 block">
                        Gebruikersnaam
                      </Label>
                      <Input
                        id="smtpUsername"
                        value={config.smtp_username || ''}
                        onChange={(e) => updateConfig('smtp_username', e.target.value)}
                        placeholder="<EMAIL>"
                        className="ai-chat-input"
                      />
                    </div>
                  )}
                  
                  {field === 'smtp_password' && (
                    <div>
                      <Label htmlFor="smtpPassword" className="text-white font-medium mb-2 block">
                        Wachtwoord / API Key
                      </Label>
                      <div className="relative">
                        <Input
                          id="smtpPassword"
                          type={showPassword ? 'text' : 'password'}
                          value={config.smtp_password || ''}
                          onChange={(e) => updateConfig('smtp_password', e.target.value)}
                          placeholder="••••••••"
                          className="ai-chat-input pr-10"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-white/60 hover:text-white"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* Provider Help */}
              <div className="bg-blue-500/10 border border-blue-400/30 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-blue-400 mt-0.5" />
                  <div>
                    <h4 className="text-blue-400 font-medium mb-1">
                      {providerConfigs[config.provider].name} Setup
                    </h4>
                    <p className="text-blue-300/80 text-sm">
                      {providerConfigs[config.provider].help}
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white font-medium">E-mail Tracking</Label>
                      <p className="text-white/60 text-sm">Track opens en clicks</p>
                    </div>
                    <Switch
                      checked={config.enable_tracking}
                      onCheckedChange={(checked) => updateConfig('enable_tracking', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white font-medium">BCC Copy</Label>
                      <p className="text-white/60 text-sm">Stuur BCC naar archief</p>
                    </div>
                    <Switch
                      checked={config.enable_bcc}
                      onCheckedChange={(checked) => updateConfig('enable_bcc', checked)}
                    />
                  </div>

                  {config.enable_bcc && (
                    <div>
                      <Label htmlFor="bccAddress" className="text-white font-medium mb-2 block">
                        BCC Adres
                      </Label>
                      <Input
                        id="bccAddress"
                        type="email"
                        value={config.bcc_address || ''}
                        onChange={(e) => updateConfig('bcc_address', e.target.value)}
                        placeholder="<EMAIL>"
                        className="ai-chat-input"
                      />
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-white font-medium">DKIM Signing</Label>
                      <p className="text-white/60 text-sm">Verbeter e-mail deliverability</p>
                    </div>
                    <Switch
                      checked={config.enable_dkim}
                      onCheckedChange={(checked) => updateConfig('enable_dkim', checked)}
                    />
                  </div>

                  {config.enable_dkim && (
                    <>
                      <div>
                        <Label htmlFor="dkimSelector" className="text-white font-medium mb-2 block">
                          DKIM Selector
                        </Label>
                        <Input
                          id="dkimSelector"
                          value={config.dkim_selector || ''}
                          onChange={(e) => updateConfig('dkim_selector', e.target.value)}
                          placeholder="default"
                          className="ai-chat-input"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="dkimPrivateKey" className="text-white font-medium mb-2 block">
                          DKIM Private Key
                        </Label>
                        <Textarea
                          id="dkimPrivateKey"
                          value={config.dkim_private_key || ''}
                          onChange={(e) => updateConfig('dkim_private_key', e.target.value)}
                          placeholder="-----BEGIN PRIVATE KEY-----..."
                          className="ai-chat-input font-mono text-xs"
                          rows={4}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="glass-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <Shield className="h-5 w-5 text-green-400" />
                      <h3 className="font-semibold text-white">Beveiligingsstatus</h3>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-white/60">Connectie:</span>
                        <Badge className="bg-green-500/20 text-green-400">TLS/SSL</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-white/60">Authenticatie:</span>
                        <Badge className="bg-green-500/20 text-green-400">Enabled</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-white/60">Tracking:</span>
                        <Badge className={config.enable_tracking ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}>
                          {config.enable_tracking ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-card">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <Database className="h-5 w-5 text-blue-400" />
                      <h3 className="font-semibold text-white">Gebruikslimieten</h3>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-white/60">Dagelijks limiet:</span>
                        <span className="text-white">{config.max_emails_per_day}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-white/60">Verstuurd vandaag:</span>
                        <span className="text-white">0</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-white/60">Resterend:</span>
                        <span className="text-green-400">{config.max_emails_per_day}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="glass-card">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Zap className="h-5 w-5 text-yellow-400" />
                    <h3 className="font-semibold text-white">Aanbevelingen</h3>
                  </div>
                  <ul className="space-y-2 text-sm text-white/80">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span>Gebruik altijd TLS/SSL voor SMTP connecties</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span>Enable DKIM voor betere deliverability</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span>Monitor je e-mail reputation regelmatig</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span>Gebruik dedicated IP voor grote volumes</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}