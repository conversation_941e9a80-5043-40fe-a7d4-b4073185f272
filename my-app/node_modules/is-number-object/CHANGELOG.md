# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.1](https://github.com/inspect-js/is-number-object/compare/v1.1.0...v1.1.1) - 2024-12-15

### Commits

- [Dev <PERSON>] update `@arethetypeswrong/cli`,` @ljharb/tsconfig`, `@types/tape` [`00d566d`](https://github.com/inspect-js/is-number-object/commit/00d566d869ee316c896aa6f3cd694996bab6f482)
- [Refactor] use `call-bound` directly [`073d5df`](https://github.com/inspect-js/is-number-object/commit/073d5df97278ab54e32750f24d4eeee1d94965d4)
- [Deps] update `call-bind` [`36c84af`](https://github.com/inspect-js/is-number-object/commit/36c84afd5553a538cceb3da56a8721b597f540bc)

## [v1.1.0](https://github.com/inspect-js/is-number-object/compare/v1.0.7...v1.1.0) - 2024-12-01

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`cb8423c`](https://github.com/inspect-js/is-number-object/commit/cb8423cd42bded7c9321e785a97c5305c2706b02)
- [New] add types [`273e406`](https://github.com/inspect-js/is-number-object/commit/273e4063e786210ce135237f1232630eecc22a88)
- [actions] split out node 10-20, and 20+ [`3da6267`](https://github.com/inspect-js/is-number-object/commit/****************************************)
- [Robustness] use `call-bind` [`834c098`](https://github.com/inspect-js/is-number-object/commit/834c09801d923ddf638585a94020b7c3b3cec3dc)
- [actions] update rebase action to use reusable workflow [`84a8a9f`](https://github.com/inspect-js/is-number-object/commit/84a8a9f61b1e098cba7d2603d98c06fc96b60d60)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `core-js`, `npmignore`, `tape` [`7275bca`](https://github.com/inspect-js/is-number-object/commit/7275bcad3910fe3073ca960fdb8018904f4eb5a0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `core-js`, `tape` [`49a83aa`](https://github.com/inspect-js/is-number-object/commit/49a83aa830081afcbeae32adcd853f19202acc89)
- [Tests] replace `aud` with `npm audit` [`061492b`](https://github.com/inspect-js/is-number-object/commit/061492b782012e0d58714bdf8a1423910d6ea49a)
- [Refactor] avoid an expensive check, for null [`08d29a8`](https://github.com/inspect-js/is-number-object/commit/08d29a8442f5340eedc3817eddd8d1f4bfd02be2)
- [Deps] update `has-tostringtag` [`4e2ad65`](https://github.com/inspect-js/is-number-object/commit/4e2ad656b23fcfdc3fe8979c7865f501f49c4704)
- [Dev Deps] add missing peer dep [`8228bfa`](https://github.com/inspect-js/is-number-object/commit/8228bfa94317d0cd5a5e880991cb3c0f0c5e119b)

## [v1.0.7](https://github.com/inspect-js/is-number-object/compare/v1.0.6...v1.0.7) - 2022-04-01

### Commits

- [actions] reuse common workflows [`8f9a1b0`](https://github.com/inspect-js/is-number-object/commit/8f9a1b040a435a5c5d12150952d0e6f96d4f713a)
- [meta] better `eccheck` command [`9dc8dff`](https://github.com/inspect-js/is-number-object/commit/9dc8dff273e4d0b954fd4f2a2eacc849d321b646)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `core-js`, `safe-publish-latest`, `tape` [`c50ecbf`](https://github.com/inspect-js/is-number-object/commit/c50ecbfc7577c69324fca4d2b40dd354c5156be9)
- [actions] update codecov uploader [`f1a2560`](https://github.com/inspect-js/is-number-object/commit/f1a2560d4a996abea90f7a792069a35359869b67)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `core-js`, `tape` [`4b06ace`](https://github.com/inspect-js/is-number-object/commit/4b06aceeba2ebf0887af020aa89caad8950e5f47)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `core-js`, `tape` [`3dc0e8b`](https://github.com/inspect-js/is-number-object/commit/3dc0e8b0fcc96a9bc9936e9a62a523fe67bafca5)
- [meta] add `bugs`/`homepage` package.json fields [`d7e0bcf`](https://github.com/inspect-js/is-number-object/commit/d7e0bcf1fe1b4a48d6500266c8b4058c854fecba)

## [v1.0.6](https://github.com/inspect-js/is-number-object/compare/v1.0.5...v1.0.6) - 2021-08-05

### Commits

- [Tests] run tests with core-js as well [`5177312`](https://github.com/inspect-js/is-number-object/commit/51773120b18e27bfe8a3bd228ef2e21f5802f338)
- [Refactor] use `has-tostringtag` to behave correctly in the presence of symbol shams [`ca2b31d`](https://github.com/inspect-js/is-number-object/commit/ca2b31d81c5d7d9b11e812dee58cd627a6d634e2)
- [Dev Deps] update `auto-changelog`, `core-js`, `eslint`, `tape` [`50950f9`](https://github.com/inspect-js/is-number-object/commit/50950f962a4b1188c478f6034194d7eb4314c884)

## [v1.0.5](https://github.com/inspect-js/is-number-object/compare/v1.0.4...v1.0.5) - 2021-05-07

### Commits

- [Tests] migrate tests to Github Actions [`9666737`](https://github.com/inspect-js/is-number-object/commit/96667372f8e36f70516218f86318f957f8c175ad)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`7815ce2`](https://github.com/inspect-js/is-number-object/commit/7815ce21cb5662c2d1651b3ec302f186aa8a016b)
- [meta] do not publish github action workflow files [`80ccb75`](https://github.com/inspect-js/is-number-object/commit/80ccb7509f91732675b018cc1a636d649a92889e)
- [Tests] run `nyc` on all tests [`c9ffb74`](https://github.com/inspect-js/is-number-object/commit/c9ffb74443690ef22f9aa7dd35855fd1e3be5184)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`7e84161`](https://github.com/inspect-js/is-number-object/commit/7e84161d089c87ef42e3639ac1889642624ebd28)
- [readme] add actions and codecov badges [`0c5ec7a`](https://github.com/inspect-js/is-number-object/commit/0c5ec7aa87dac27bdcda2365124c3aa0ccf9c278)
- [actions] add Require Allow Edits workflow [`dd0fb74`](https://github.com/inspect-js/is-number-object/commit/dd0fb74b2ecb630ea7778a6f06dcc017323a3c1d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `has-symbols`, `tape` [`2d36f80`](https://github.com/inspect-js/is-number-object/commit/2d36f809a7b9896958b0b0f3b69be0067caedb45)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`77d3140`](https://github.com/inspect-js/is-number-object/commit/77d3140557d483e467ce070b21bf384e9a7562d5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`75d4abf`](https://github.com/inspect-js/is-number-object/commit/75d4abf34168e69d73f621c696a16179ddc0873c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`0c2a917`](https://github.com/inspect-js/is-number-object/commit/0c2a917e4802b102888759fad912bd9faa5587f7)
- [Fix] do not use `Object.prototype.toString` when `Symbol.toStringTag` is shammed [`8b6ebc4`](https://github.com/inspect-js/is-number-object/commit/8b6ebc489db14a0c369214e081413f326fc0d598)
- [Dev Deps] update `auto-changelog`, `tape`; add `aud` [`62045fc`](https://github.com/inspect-js/is-number-object/commit/62045fcaddb9e4d3ef81068e99d07d21cd62023b)
- [actions] use checkout v2; remove unneeded env [`d48cd06`](https://github.com/inspect-js/is-number-object/commit/d48cd06720ea71f278a6d35c6f0a8ec04242a58f)
- [meta] use `prepublishOnly` script for npm 7+ [`827ab0d`](https://github.com/inspect-js/is-number-object/commit/827ab0d52d25f46d232ae7442ece270dec2de1df)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`bfed500`](https://github.com/inspect-js/is-number-object/commit/bfed500e6cc3cd3b9e7ffea78429c59857035791)
- [meta] remove explicit audit level config [`ce23e5e`](https://github.com/inspect-js/is-number-object/commit/ce23e5e49fbebe190267d8c99ddfd880a963b7ee)
- [meta] gitignore coverage output [`f1ad981`](https://github.com/inspect-js/is-number-object/commit/f1ad98106549c1c88322d8cb206068ea4c5bd424)

## [v1.0.4](https://github.com/inspect-js/is-number-object/compare/v1.0.3...v1.0.4) - 2019-12-18

### Commits

- [Tests] use shared travis-ci configs [`792b5aa`](https://github.com/inspect-js/is-number-object/commit/792b5aa5e7313ddf5507f7283bb7d5d5c646b11b)
- [Tests] up to `node` `v12.4`, `v11.15`, `v10.15`, `v9.11`, `v8.15`, `v7.10`, `v6.17`, `v5.12`, `v4.9`; use `nvm install-latest-npm` [`dc66db7`](https://github.com/inspect-js/is-number-object/commit/dc66db7dd1eca0263f6602597eb40601519e912e)
- Update `eslint`, `tape`, `semver`; use my personal shared `eslint` config [`7660fed`](https://github.com/inspect-js/is-number-object/commit/7660fed03a7060eb5c91e74b9a17303d4fac1056)
- [Tests] remove `jscs` [`f1fee97`](https://github.com/inspect-js/is-number-object/commit/f1fee97423478bcc653c844fadda55138d9b9a54)
- [meta] add `auto-changelog` [`4b1c225`](https://github.com/inspect-js/is-number-object/commit/4b1c2253770eb18761a1e8b157772028d6f742c4)
- [meta] remove unused Makefile and associated utilities [`379b979`](https://github.com/inspect-js/is-number-object/commit/379b9793d9c61d7889e53bd9de9578dca9964ebc)
- Update `covert`, `jscs`, `eslint`, `semver` [`16d2af8`](https://github.com/inspect-js/is-number-object/commit/16d2af82a6c93aee614f7a4b2c468411c743e95f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `is`, `replace`, `semver`, `tape` [`21c0f04`](https://github.com/inspect-js/is-number-object/commit/21c0f0431984b87443c6acb9f003368feb7b4368)
- Update `is`, `tape`, `covert`, `jscs`, `editorconfig-tools`, `nsp`, `eslint`, `semver`. Add `replace`. Use `^` instead of `~`. [`19d6ee3`](https://github.com/inspect-js/is-number-object/commit/19d6ee3a3d4a87764d57316804fd8b882ba5197c)
- Update `eslint` [`d32754b`](https://github.com/inspect-js/is-number-object/commit/d32754bcca0033e01eba531c4353d1239e992203)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `replace` [`1df8165`](https://github.com/inspect-js/is-number-object/commit/1df8165dd63d9f2f78ccb78e905d0a6b3e302884)
- Update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`675372b`](https://github.com/inspect-js/is-number-object/commit/675372b115fb20b5034f40bcbb5560c6c0512746)
- [readme] clean up readme; remove testling; fix repo URLs [`80e29c4`](https://github.com/inspect-js/is-number-object/commit/80e29c4d6d0811fc361e95ee83b81280bf3ae3f5)
- [Tests] up to `node` `v12.7`, `v10.16`, `v8.16` [`287a968`](https://github.com/inspect-js/is-number-object/commit/287a9687b1fc3d091ec231c06f19a19ff7b0e8f6)
- Test on latest `iojs` and `node` versions. [`11c98a2`](https://github.com/inspect-js/is-number-object/commit/11c98a23b232cb21c7daab797fd63875c2970681)
- [actions] add automatic rebasing / merge commit blocking [`022d026`](https://github.com/inspect-js/is-number-object/commit/022d026129df445f239ba2ecd8d47a2786242d75)
- [meta] create FUNDING.yml [`7f52710`](https://github.com/inspect-js/is-number-object/commit/7f527107168aad7108b7c262d295dcf44e03214d)
- [Dev Deps] update `is`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver` [`bc8cd50`](https://github.com/inspect-js/is-number-object/commit/bc8cd508fe4440168f9b049be3ddf93c56c06c49)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`1f9200b`](https://github.com/inspect-js/is-number-object/commit/1f9200b7c56840dc23eeeca5d0ee4f64a0446e08)
- [Tests] up to `node` `v12.11` [`706d50a`](https://github.com/inspect-js/is-number-object/commit/706d50a779b90feb3f4d2ae88d8189d19b913073)
- [Dev Deps] update `jscs` [`e3591a4`](https://github.com/inspect-js/is-number-object/commit/e3591a445b1af25d46632eafea51efa07b4eb6dc)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`baf4ee7`](https://github.com/inspect-js/is-number-object/commit/baf4ee749fb65ec12e9cab102e77aa0e14312109)
- Update `nsp`, `eslint` [`61b18d5`](https://github.com/inspect-js/is-number-object/commit/61b18d5b44542fddf4950534d506b20d8c8b1f44)
- Update `eslint`, `semver` [`52e61bd`](https://github.com/inspect-js/is-number-object/commit/52e61bd4334c0a1afacd147fd0bc1e2c1be10df5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `has-symbols`; add `safe-publish-latest` [`79db7f6`](https://github.com/inspect-js/is-number-object/commit/79db7f610d2bcf5f0d6e8ca834f7402504101072)
- Only apps should have lockfiles [`677b9b4`](https://github.com/inspect-js/is-number-object/commit/677b9b4fb6ad9d7b984cb0f89c8b5a6df143b29a)
- Test on `io.js` `v2.2` [`e8a38b2`](https://github.com/inspect-js/is-number-object/commit/e8a38b2fe73b841b0ed55d9f60573d460a4f2a62)
- [meta] add `funding` field [`85315e7`](https://github.com/inspect-js/is-number-object/commit/85315e75c119a2aef70a766f2ddc1079b64d006b)
- [Dev Deps] update `eslint`, `tape` [`f3581aa`](https://github.com/inspect-js/is-number-object/commit/f3581aaea310546f6ee4612990468d39f058d320)
- [Tests] use `eclint` instead of `editorconfig-tools` [`7b53680`](https://github.com/inspect-js/is-number-object/commit/7b5368071000eb1c715aeeee5ff47ffdbee9fe5c)
- [Dev Deps] update `semver`, `tape` [`d6b524a`](https://github.com/inspect-js/is-number-object/commit/d6b524ac2e8c0240c436cbe8828671e383d51fd5)
- [Dev Deps] Update `tape`, `eslint` [`be19203`](https://github.com/inspect-js/is-number-object/commit/be19203dee0aa70ff8f09823bf880a38b824e1ed)
- Test up to `io.js` `v2.1` [`feb7ba6`](https://github.com/inspect-js/is-number-object/commit/feb7ba63a0816f1d36419ce240f96e9b4e4c90ba)
- Test up to `io.js` `v3.0` [`7be1f0a`](https://github.com/inspect-js/is-number-object/commit/7be1f0a25dc59b6606be9ee1ace38cb7039a59d2)
- [Dev Deps] update `tape` [`d9a2318`](https://github.com/inspect-js/is-number-object/commit/d9a2318bc82477e9321e961def11e28d364e5562)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`a6cd411`](https://github.com/inspect-js/is-number-object/commit/a6cd411c6bd92691a48b52683afce584c2c6b21b)
- Test on `io.js` `v2.4` [`46c2e7f`](https://github.com/inspect-js/is-number-object/commit/46c2e7f2ce8ad7f8ab3c1da827d93fc2780eff06)
- Test on `io.js` `v2.3` [`9c344b0`](https://github.com/inspect-js/is-number-object/commit/9c344b0df83628908a1f776a3f2e5fc4fae1d4d2)
- Fix tests for faked @@toStringTag [`f8c446e`](https://github.com/inspect-js/is-number-object/commit/f8c446e9fc320c23807717356e259529f494b9f3)

## [v1.0.3](https://github.com/inspect-js/is-number-object/compare/v1.0.2...v1.0.3) - 2015-01-29

### Commits

- If @@toStringTag is not present, use the old-school Object#toString test. [`9b2a4df`](https://github.com/inspect-js/is-number-object/commit/9b2a4df6ccf903e89198d4244eeb7f47a7056327)

## [v1.0.2](https://github.com/inspect-js/is-number-object/compare/v1.0.1...v1.0.2) - 2015-01-29

### Commits

- Improve optimizability of the non-try/catch part. [`7e6be2f`](https://github.com/inspect-js/is-number-object/commit/7e6be2fd2346557fc81bd544ac8745021c50e266)
- Fix package.json [`4f2ebea`](https://github.com/inspect-js/is-number-object/commit/4f2ebeae09c45e1eefeb2c10a011ff2ef0aca921)

## [v1.0.1](https://github.com/inspect-js/is-number-object/compare/v1.0.0...v1.0.1) - 2015-01-29

### Commits

- Use Object() instead of new Number() [`1aaa746`](https://github.com/inspect-js/is-number-object/commit/1aaa746c26878a0f698aabea4d88215311f2a38d)
- Add early exits for typeof number, or typeof not "object". [`eae4337`](https://github.com/inspect-js/is-number-object/commit/eae43375d3f88e04bb10eabd954e5a6b66ad5305)

## v1.0.0 - 2015-01-28

### Commits

- Dotfiles. [`9c74e3e`](https://github.com/inspect-js/is-number-object/commit/9c74e3eb2b10398d4022de7c4015531e874f06c8)
- `make release` [`a99e5ae`](https://github.com/inspect-js/is-number-object/commit/a99e5aeb3995a7d543fc5833722bc02011fabad6)
- package.json [`4fed9ef`](https://github.com/inspect-js/is-number-object/commit/4fed9ef7c35ccfc45ca8acd3c92c9cb91c7daa6d)
- Read me [`c91d6ba`](https://github.com/inspect-js/is-number-object/commit/c91d6ba00de79eaaac5fec7c9d8866d61d0abb62)
- Initial commit [`629fb96`](https://github.com/inspect-js/is-number-object/commit/629fb969f076e0802c799b368c7b02556bb0750e)
- Tests. [`a39de62`](https://github.com/inspect-js/is-number-object/commit/a39de624785cc204ed7c0ea5518f1c878870ceb1)
- Implementation. [`aedd91e`](https://github.com/inspect-js/is-number-object/commit/aedd91e6fc23f00852ad1266b6c19f32b7f93a22)
