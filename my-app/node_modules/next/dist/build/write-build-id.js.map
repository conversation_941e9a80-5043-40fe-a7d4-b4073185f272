{"version": 3, "sources": ["../../src/build/write-build-id.ts"], "sourcesContent": ["import { promises } from 'fs'\nimport { join } from 'path'\nimport { BUILD_ID_FILE } from '../shared/lib/constants'\n\nexport async function writeBuildId(\n  distDir: string,\n  buildId: string\n): Promise<void> {\n  const buildIdPath = join(distDir, BUILD_ID_FILE)\n  await promises.writeFile(buildIdPath, buildId, 'utf8')\n}\n"], "names": ["writeBuildId", "distDir", "buildId", "buildIdPath", "join", "BUILD_ID_FILE", "promises", "writeFile"], "mappings": ";;;;+BAIsBA;;;eAAAA;;;oBAJG;sBACJ;2BACS;AAEvB,eAAeA,aACpBC,OAAe,EACfC,OAAe;IAEf,MAAMC,cAAcC,IAAAA,UAAI,EAACH,SAASI,wBAAa;IAC/C,MAAMC,YAAQ,CAACC,SAAS,CAACJ,aAAaD,SAAS;AACjD", "ignoreList": [0]}