{"version": 3, "sources": ["../../src/build/worker.ts"], "sourcesContent": ["import '../server/require-hook'\n\nexport {\n  getDefinedNamedExports,\n  hasCustomGetInitialProps,\n  isPageStatic,\n} from './utils'\nexport { exportPages } from '../export/worker'\n"], "names": ["exportPages", "getDefinedNamedExports", "hasCustomGetInitialProps", "isPageStatic"], "mappings": ";;;;;;;;;;;;;;;;;IAOSA,WAAW;eAAXA,mBAAW;;IAJlBC,sBAAsB;eAAtBA,6BAAsB;;IACtBC,wBAAwB;eAAxBA,+BAAwB;;IACxBC,YAAY;eAAZA,mBAAY;;;QALP;uBAMA;wBACqB", "ignoreList": [0]}