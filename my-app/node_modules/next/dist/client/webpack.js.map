{"version": 3, "sources": ["../../src/client/webpack.ts"], "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/no-unused-vars\ndeclare const __webpack_require__: any\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\ndeclare let __webpack_public_path__: string\n\nimport { getDeploymentIdQueryOrEmptyString } from '../build/deployment-id'\n\n// If we have a deployment ID, we need to append it to the webpack chunk names\n// I am keeping the process check explicit so this can be statically optimized\nif (process.env.NEXT_DEPLOYMENT_ID) {\n  const suffix = getDeploymentIdQueryOrEmptyString()\n  // eslint-disable-next-line no-undef\n  const getChunkScriptFilename = __webpack_require__.u\n  // eslint-disable-next-line no-undef\n  __webpack_require__.u = (...args: any[]) =>\n    // We enode the chunk filename because our static server matches against and encoded\n    // filename path.\n    getChunkScriptFilename(...args) + suffix\n\n  // eslint-disable-next-line no-undef\n  const getChunkCssFilename = __webpack_require__.k\n  // eslint-disable-next-line no-undef\n  __webpack_require__.k = (...args: any[]) =>\n    getChunkCssFilename(...args) + suffix\n\n  // eslint-disable-next-line no-undef\n  const getMiniCssFilename = __webpack_require__.miniCssF\n  // eslint-disable-next-line no-undef\n  __webpack_require__.miniCssF = (...args: any[]) =>\n    getMiniCssFilename(...args) + suffix\n}\n\n// Ignore the module ID transform in client.\n;(self as any).__next_set_public_path__ = (path: string) => {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  __webpack_public_path__ = path\n}\n\nexport {}\n"], "names": ["process", "env", "NEXT_DEPLOYMENT_ID", "suffix", "getDeploymentIdQueryOrEmptyString", "getChunkScriptFilename", "__webpack_require__", "u", "args", "getChunkCssFilename", "k", "getMiniCssFilename", "miniCssF", "self", "__next_set_public_path__", "path", "__webpack_public_path__"], "mappings": "AAAA,6DAA6D;;;;;8BAKX;AAElD,8EAA8E;AAC9E,8EAA8E;AAC9E,IAAIA,QAAQC,GAAG,CAACC,kBAAkB,EAAE;IAClC,MAAMC,SAASC,IAAAA,+CAAiC;IAChD,oCAAoC;IACpC,MAAMC,yBAAyBC,oBAAoBC,CAAC;IACpD,oCAAoC;IACpCD,oBAAoBC,CAAC,GAAG;yCAAIC;YAAAA;;eAC1B,oFAAoF;QACpF,iBAAiB;QACjBH,0BAA0BG,QAAQL;;IAEpC,oCAAoC;IACpC,MAAMM,sBAAsBH,oBAAoBI,CAAC;IACjD,oCAAoC;IACpCJ,oBAAoBI,CAAC,GAAG;yCAAIF;YAAAA;;eAC1BC,uBAAuBD,QAAQL;;IAEjC,oCAAoC;IACpC,MAAMQ,qBAAqBL,oBAAoBM,QAAQ;IACvD,oCAAoC;IACpCN,oBAAoBM,QAAQ,GAAG;yCAAIJ;YAAAA;;eACjCG,sBAAsBH,QAAQL;;AAClC;AAGEU,KAAaC,wBAAwB,GAAG,CAACC;IACzC,6DAA6D;IAC7DC,0BAA0BD;AAC5B", "ignoreList": [0]}