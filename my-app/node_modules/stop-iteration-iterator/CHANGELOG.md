# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.0](https://github.com/ljharb/stop-iteration-iterator/compare/v1.0.0...v1.1.0) - 2024-12-13

### Commits

- [New] add types [`f0ee985`](https://github.com/ljharb/stop-iteration-iterator/commit/f0ee985afeccf2950e693757266c77baeaab50f5)
- [actions] split out node 10-20, and 20+ [`b49d910`](https://github.com/ljharb/stop-iteration-iterator/commit/b49d9101ff21b27e30ce6b2c52e121b36efaa7e8)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `tape` [`2bdaff7`](https://github.com/ljharb/stop-iteration-iterator/commit/2bdaff787a45d4034c079bd308c4a9ba2b09950e)
- [types] clean up tsconfig [`c275b15`](https://github.com/ljharb/stop-iteration-iterator/commit/c275b15940ed12955e77bf2fd764ff507788637d)
- [meta] clean up changelog [`a3eeb9a`](https://github.com/ljharb/stop-iteration-iterator/commit/a3eeb9a763063e27a560f09d25b865f40e373154)
- [Robustness] use `es-errors` [`ca51cb8`](https://github.com/ljharb/stop-iteration-iterator/commit/ca51cb8d7ef259e4aa8e4a9289f0c187fc08d560)
- [Tests] replace `aud` with `npm audit` [`d4b8df0`](https://github.com/ljharb/stop-iteration-iterator/commit/d4b8df04328f133b65bec028c5f78715cd061357)
- [Deps] update `internal-slot` [`61ca626`](https://github.com/ljharb/stop-iteration-iterator/commit/61ca626be1bd5e9bb6051f70b32e3d49b8abcd6a)
- [Deps] update `internal-slot` [`cdabf84`](https://github.com/ljharb/stop-iteration-iterator/commit/cdabf8419d448faaacd1b5d87c839acb7a6eb899)
- [meta] add `sideEffects` flag [`a5f6cb6`](https://github.com/ljharb/stop-iteration-iterator/commit/a5f6cb6e9fa8859115e452c22d4ef90366dc4d0f)
- [Dev Deps] add missing peer dep [`6f6496e`](https://github.com/ljharb/stop-iteration-iterator/commit/6f6496ec5c47499f5e30b80268e2ce545a5469ba)

## v1.0.0 - 2023-01-12

### Commits

- Initial implementation, tests, readme [`43e8109`](https://github.com/ljharb/stop-iteration-iterator/commit/43e81099d2f2b63ff3a8a253ad13dd8279c9e2dc)
- Initial commit [`23929ce`](https://github.com/ljharb/stop-iteration-iterator/commit/23929ce525165bfe54f053284fd066dce8598486)
- npm init [`a9847ab`](https://github.com/ljharb/stop-iteration-iterator/commit/a9847ab637a7c223fb7478d47caf04e89ba283ff)
- Only apps should have lockfiles [`4e41f3f`](https://github.com/ljharb/stop-iteration-iterator/commit/4e41f3fbbaf8a1d32b12514d7296961e5df73e4b)
